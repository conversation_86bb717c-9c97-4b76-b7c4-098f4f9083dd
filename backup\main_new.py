import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import threading
import time
import logging
import sys
import os
import queue

# 导入自定义模块（如果有的话）
try:
    from backup.pitch_detector import PitchDetector
except ImportError:
    print("警告：无法导入音高检测器模块，部分功能可能不可用")
    PitchDetector = None

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('PitchPracticeApp')

class MainApp:
    """乐器音准训练系统主应用"""
    
    def __init__(self, root):
        """初始化主应用"""
        self.root = root
        self.root.title("乐器音准训练系统")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # 设置窗口最大化
        
        # 设置背景颜色
        self.root.configure(bg="#f5f5f5")
        
        # 定义字体样式，增大字体尺寸
        self.title_font = ("Microsoft YaHei", 36, "bold")
        self.subtitle_font = ("Microsoft YaHei", 18)
        self.button_font = ("Microsoft YaHei", 16)
        self.label_font = ("Microsoft YaHei", 14)
        
        # 创建主界面
        self.create_main_interface()
    
    def create_main_interface(self):
        """创建主界面"""
        # 清除可能存在的旧组件
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 创建主容器框架，使用填充设计
        main_container = tk.Frame(self.root, bg="#f5f5f5")
        main_container.pack(fill="both", expand=True)
        
        # 创建主内容区域
        content_frame = tk.Frame(main_container, bg="#ffffff")
        content_frame.pack(fill="both", expand=True)
        
        # 顶部装饰性条
        top_bar = tk.Frame(content_frame, bg="#3f51b5", height=12)
        top_bar.pack(fill="x")
        
        # 标题区域
        title_frame = tk.Frame(content_frame, bg="#ffffff", pady=60)  # 从40增加到60
        title_frame.pack(fill="x")
        
        # 标题和图标
        title_container = tk.Frame(title_frame, bg="#ffffff")
        title_container.pack()
        
        # 标题图标
        icon_frame = tk.Frame(title_container, bg="#3f51b5", width=70, height=70)
        icon_frame.pack(side=tk.LEFT, padx=(0, 25))
        icon_frame.pack_propagate(False)
        
        # 音符图标
        tk.Label(
            icon_frame,
            text="♪",
            font=("Arial", 40, "bold"),
            bg="#3f51b5",
            fg="white"
        ).place(relx=0.5, rely=0.5, anchor="center")
        
        # 标题文字
        title_label = tk.Label(
            title_container,
            text="乐器音准训练系统",
            font=self.title_font,
            bg="#ffffff",
            fg="#333333"
        )
        title_label.pack(side=tk.LEFT)
        
        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="提高演奏技巧，精确掌握音准",
            font=self.subtitle_font,
            bg="#ffffff",
            fg="#666666"
        )
        subtitle_label.pack(pady=(15, 0))
        
        # 主要功能按钮区域
        buttons_frame = tk.Frame(content_frame, bg="#ffffff", pady=60)  # 从40增加到60
        buttons_frame.pack()
        
        # 创建按钮容器，使用固定宽度确保对齐
        button_container = tk.Frame(buttons_frame, bg="#ffffff", width=600)
        button_container.pack()
        
        # 单音音准练习按钮 - 使用真正的Button，图标和文字在同一行
        single_button_frame = tk.Frame(button_container, bg="#ffffff", pady=15)
        single_button_frame.pack()
        
        # 创建单音练习按钮
        single_note_button = tk.Button(
            single_button_frame,
            text="♪ 单音音准练习",
            font=("Microsoft YaHei", 18, "bold"),
            bg="#4285F4",
            fg="white",
            padx=30,
            pady=15,
            compound=tk.LEFT,
            command=self.open_single_note_practice,
            width=20,  # 增加宽度
            cursor="hand2"
        )
        single_note_button.pack()
        
        # 单音按钮说明文字
        tk.Label(
            single_button_frame,
            text="逐个练习音符的准确度",
            font=self.label_font,
            bg="#ffffff",
            fg="#666666"
        ).pack(pady=(10, 0))
        
        # 多音音准练习按钮 - 使用真正的Button，图标和文字在同一行
        multi_button_frame = tk.Frame(button_container, bg="#ffffff", pady=15)
        multi_button_frame.pack()
        
        # 创建多音练习按钮
        multi_note_button = tk.Button(
            multi_button_frame,
            text="♫ 多音音准练习",
            font=("Microsoft YaHei", 18, "bold"),
            bg="#34A853",
            fg="white",
            padx=30,
            pady=15,
            compound=tk.LEFT,
            command=self.open_multi_note_practice,
            width=20,  # 增加宽度
            cursor="hand2"
        )
        multi_note_button.pack()
        
        # 多音按钮说明文字
        tk.Label(
            multi_button_frame,
            text="练习和弦与连续音符",
            font=self.label_font,
            bg="#ffffff",
            fg="#666666"
        ).pack(pady=(10, 0))
        
        # 底部工具按钮区域
        tools_frame = tk.Frame(content_frame, bg="#ffffff", pady=60)  # 从40增加到60
        tools_frame.pack()
        
        # 底部按钮容器 - 使用居中布局让两个按钮之间有合适间距
        bottom_buttons = tk.Frame(tools_frame, bg="#ffffff", width=600)
        bottom_buttons.pack()
        
        # 创建两个按钮的容器框架
        bottom_buttons_container = tk.Frame(bottom_buttons, bg="#ffffff")
        bottom_buttons_container.pack()
        
        # 设置按钮 - 确保与上面按钮宽度一致
        settings_button = tk.Button(
            bottom_buttons_container,
            text="⚙ 设置",
            font=("Microsoft YaHei", 16, "bold"),
            bg="#607D8B",
            fg="white",
            padx=30,
            pady=10,
            command=self.open_settings,
            width=8,  # 调整宽度
            cursor="hand2"
        )
        settings_button.pack(side=tk.LEFT, padx=5)
        
        # 历史记录按钮 - 确保与上面按钮宽度一致
        history_button = tk.Button(
            bottom_buttons_container,
            text="📊 历史记录",
            font=("Microsoft YaHei", 16, "bold"),
            bg="#9C27B0",
            fg="white",
            padx=30,
            pady=10,
            command=self.open_history,
            width=8,  # 调整宽度
            cursor="hand2"
        )
        history_button.pack(side=tk.LEFT, padx=5)
        
        # 底部说明文字区域
        footer_frame = tk.Frame(content_frame, bg="#ffffff")
        footer_frame.pack(side=tk.BOTTOM, fill="x", pady=45)  # 从30增加到45
        
        # 底部说明文字
        footer_label = tk.Label(
            footer_frame,
            text="通过练习提升您的乐器演奏音准",
            font=self.label_font,
            bg="#ffffff",
            fg="#666666"
        )
        footer_label.pack()
        
        # 版本信息
        version_label = tk.Label(
            footer_frame,
            text="v1.0.2",
            font=("Microsoft YaHei", 12),
            bg="#ffffff",
            fg="#999999"
        )
        version_label.pack(pady=(10, 0))
    
    def open_single_note_practice(self):
        """打开单音音准练习界面"""
        self.root.withdraw()  # 隐藏主窗口
        
        # 创建新窗口
        practice_window = tk.Toplevel(self.root)
        practice_window.title("单音音准练习")
        practice_window.geometry("900x700")
        practice_window.state('zoomed')  # 设置窗口最大化
        practice_window.protocol("WM_DELETE_WINDOW", lambda: self.close_practice_window(practice_window))
        
        # 初始化单音练习界面
        SingleNotePracticeApp(practice_window, self)
    
    def open_multi_note_practice(self):
        """打开多音音准练习界面"""
        self.root.withdraw()  # 隐藏主窗口
        
        # 创建新窗口
        practice_window = tk.Toplevel(self.root)
        practice_window.title("多音音准练习")
        practice_window.geometry("900x700")
        practice_window.state('zoomed')  # 设置窗口最大化
        practice_window.protocol("WM_DELETE_WINDOW", lambda: self.close_practice_window(practice_window))
        
        # 初始化多音练习界面
        MultiNotePracticeApp(practice_window, self)
    
    def open_settings(self):
        """打开设置界面"""
        settings_window = tk.Toplevel(self.root)
        settings_app = SettingsApp(settings_window, self.main_app)
        
    def change_note_group(self, notes):
        """更改音符组"""
        if notes and len(notes) > 0:
            # 更新当前音符为第一个音符
            self.current_note.set(notes[0])
            # 调用更新目标音符的方法
            self.update_target_note()
    
    def open_history(self):
        """打开历史记录界面"""
        self.root.withdraw()  # 隐藏主窗口
        
        # 创建新窗口
        history_window = tk.Toplevel(self.root)
        history_window.title("历史记录")
        history_window.geometry("800x700")
        history_window.state('zoomed')  # 设置窗口最大化
        history_window.protocol("WM_DELETE_WINDOW", lambda: self.close_practice_window(history_window))
        
        # 初始化历史记录界面
        HistoryApp(history_window, self)
    
    def close_practice_window(self, window):
        """关闭练习窗口并显示主窗口"""
        window.destroy()
        self.root.deiconify()  # 显示主窗口
        self.root.update()  # 确保窗口更新
        self.root.state('zoomed')  # 恢复最大化状态


class SingleNotePracticeApp:
    """单音音准练习应用"""
    
    def __init__(self, root, main_app):
        self.root = root
        self.main_app = main_app
        
        # 设置窗口标题和大小
        self.root.title("单音音准练习")
        self.root.geometry("1200x800")
        self.root.configure(bg="#ffffff")  # 设置白色背景
        
        # 设置字体
        self.title_font = ("Microsoft YaHei", 18, "bold")
        self.subtitle_font = ("Microsoft YaHei", 14)
        self.button_font = ("Microsoft YaHei", 12)
        self.label_font = ("Microsoft YaHei", 12)
        
        # 变量初始化
        self.current_note = tk.StringVar(value="C4")  # 默认目标音符
        self.pitch_detector = None
        self.practice_running = False
        self.audio_queue = queue.Queue()
        self.success_frames_needed = tk.IntVar(value=3)  # 达标所需连续帧数
        self.accuracy_tolerance = tk.IntVar(value=15)  # 音准容差（单位：cents）
        self.instrument_type = tk.StringVar(value="钢琴")  # 默认乐器类型
        
        # 音量与置信度阈值
        self.volume_threshold_var = tk.DoubleVar(value=0.03)
        self.confidence_threshold_var = tk.DoubleVar(value=0.35)
        
        # 新增变量以支持UI控制
        self.judgement_mode = tk.StringVar(value="frames") # "frames" or "duration"
        self.target_duration = tk.DoubleVar(value=3.0) # seconds
        
        # 新增：与时长判断相关的实例变量
        self.practice_start_time = None
        self.duration_pitch_data = []
        self.duration_success = False  # 新增：标记时长模式是否已成功完成练习
        self.final_duration = 0.0  # 新增：记录成功时的目标时长
        self.frames_success = False  # 新增：标记帧数模式是否已成功完成练习
        
        # 帧模式变量
        self.success_frames = 0  # 连续成功帧计数
        self.target_frames = self.success_frames_needed  # 目标帧数
        
        # 统计数据
        self.consecutive_success_frames = 0
        self.success_count = 0
        self.attempt_count = 0
        
        # 错误音高相关变量
        self.wrong_note_frames = 0  # 错误音符连续帧数
        self.encourage_shown = False  # 是否已经显示鼓励弹窗
        self.in_grace_period = False  # 是否处于缓冲期（开始练习后短暂时间内不计入错误）
        
        # 绘图数据
        self.time_data = []
        self.cents_data = []
        self.amplitude_data = []
        
        # 创建UI
        self.create_widgets()
        
        # 初始化音高检测器
        try:
            from backup.pitch_detector import PitchDetector
            self.pitch_detector = PitchDetector()
            self.pitch_detector.set_target_note(self.current_note.get())
            self.pitch_detector.set_accuracy_threshold(self.accuracy_tolerance.get())
            self.pitch_detector.set_amplitude_threshold(self.volume_threshold_var.get())
            self.pitch_detector.set_confidence_threshold(self.confidence_threshold_var.get())
        except Exception as e:
            messagebox.showerror("错误", f"初始化音高检测器失败: {e}")
            logger.error(f"初始化音高检测器失败: {e}")
        
        # 开始处理音频队列的定时任务
        self.root.after(100, self.process_audio_queue)
    
    def create_widgets(self):
        """创建单音练习界面组件"""
        # 导航栏
        nav_frame = tk.Frame(self.root, bg="#3f51b5", height=50)
        nav_frame.pack(fill="x")
        
        # 返回按钮
        back_button = tk.Button(
            nav_frame,
            text="返回",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=lambda: self.main_app.close_practice_window(self.root)
        )
        back_button.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 标题
        title_label = tk.Label(
            nav_frame,
            text="单音音准练习",
            font=self.title_font,
            bg="#3f51b5",
            fg="white"
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 设置按钮
        settings_button = tk.Button(
            nav_frame,
            text="设置",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=self.open_settings
        )
        settings_button.pack(side=tk.RIGHT, padx=20, pady=10)
        
        # 主体内容区
        content_frame = tk.Frame(self.root, bg="#f5f5f5")
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)  # 减小顶部底部内边距
        
        # 将内容区分为左右两部分
        left_frame = tk.Frame(content_frame, bg="#f5f5f5")
        left_frame.pack(side=tk.LEFT, fill="y", padx=(0, 10))
        
        right_frame = tk.Frame(content_frame, bg="#f5f5f5")
        right_frame.pack(side=tk.RIGHT, fill="both", expand=True)
        
        # 左侧设置区域
        self.create_settings_panel(left_frame)
        
        # 右侧内容区域 - 修改为更紧凑的布局
        # 创建一个包含五线谱和键盘的顶部区域
        top_frame = tk.Frame(right_frame, bg="#f5f5f5")
        top_frame.pack(fill="x", pady=(0, 10))
        
        # 将五线谱和钢琴键盘区域放在同一行
        # 五线谱目标音符区 - 使用左边的50%
        note_frame = tk.LabelFrame(
            top_frame,
            text="五线谱目标音符区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        note_frame.pack(side=tk.LEFT, fill="both", expand=True, padx=(0, 5))
        
        # 目标音符显示 - 减小字体
        self.target_note_label = tk.Label(
            note_frame,
            text=self.current_note.get(),
            font=("Microsoft YaHei", 48, "bold"),  # 从60减小到48
            bg="#ffffff",
            fg="#3f51b5"
        )
        self.target_note_label.pack(pady=5)  # 减小内边距
        
        # 钢琴键盘对应区 - 使用右边的50%
        keyboard_frame = tk.LabelFrame(
            top_frame,
            text="钢琴键盘对应区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        keyboard_frame.pack(side=tk.RIGHT, fill="both", expand=True, padx=(5, 0))
        
        # 简单的键盘显示
        self.create_piano_keyboard(keyboard_frame)
        
        # 音高检测反馈区
        feedback_frame = tk.LabelFrame(
            right_frame,
            text="音高检测反馈区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        feedback_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 创建音高显示图表
        self.create_pitch_chart(feedback_frame)
        
        # 音量显示区域
        volume_frame = tk.Frame(feedback_frame, bg="#ffffff")
        volume_frame.pack(fill="x", pady=(5, 10))
        
        tk.Label(
            volume_frame,
            text="音量:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        # 创建音量条容器
        volume_bar_frame = tk.Frame(volume_frame, bg="#ffffff", width=200, height=20)
        volume_bar_frame.pack(side=tk.LEFT, padx=5)
        volume_bar_frame.pack_propagate(False)  # 固定大小
        
        # 创建音量条
        self.volume_bar = tk.Canvas(volume_bar_frame, width=200, height=20, bg="#f0f0f0", highlightthickness=0)
        self.volume_bar.pack(fill="both")
        
        # 当前音高信息显示 - 合并到一行显示更多信息
        info_frame = tk.Frame(feedback_frame, bg="#ffffff")
        info_frame.pack(fill="x", pady=5)  # 减少内边距
        
        # 第一列: 目标音和当前音
        info_col1 = tk.Frame(info_frame, bg="#ffffff")
        info_col1.pack(side=tk.LEFT, fill="x", expand=True)
        
        info_row1 = tk.Frame(info_col1, bg="#ffffff")
        info_row1.pack(fill="x")
        
        tk.Label(
            info_row1,
            text="目标音:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.target_info_label = tk.Label(
            info_row1,
            text=self.current_note.get(),
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff"
        )
        self.target_info_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            info_row1,
            text="当前音:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.current_pitch_label = tk.Label(
            info_row1,
            text="-- Hz",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.current_pitch_label.pack(side=tk.LEFT)
        
        # 第二列: 偏差和达标
        info_col2 = tk.Frame(info_frame, bg="#ffffff")
        info_col2.pack(side=tk.LEFT, fill="x", expand=True)
        
        info_row2 = tk.Frame(info_col2, bg="#ffffff")
        info_row2.pack(fill="x")
        
        tk.Label(
            info_row2,
            text="偏差:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.deviation_label = tk.Label(
            info_row2,
            text="-- cents",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.deviation_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            info_row2,
            text="达标:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.success_frames_label = tk.Label(
            info_row2,
            text=f"0/{self.success_frames_needed.get()}帧",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.success_frames_label.pack(side=tk.LEFT)
        
        # 状态信息显示
        status_frame = tk.Frame(feedback_frame, bg="#ffffff")
        status_frame.pack(fill="x", pady=(5, 0))
        
        tk.Label(
            status_frame,
            text="状态:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.status_text_label = tk.Label(
            status_frame,
            text="等待开始",
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff",
            fg="#2196f3"
        )
        self.status_text_label.pack(side=tk.LEFT)
        
        # 底部控制区域 - 直接放在主窗口底部
        bottom_frame = tk.Frame(self.root, bg="#f5f5f5")
        bottom_frame.pack(side="bottom", fill="x", padx=20, pady=15)  # 添加内边距使按钮与边缘有距离
        
        # 创建一个容器来居中放置按钮
        button_container = tk.Frame(bottom_frame, bg="#f5f5f5")
        button_container.pack(fill="x")
        
        # 创建一个子容器来保持按钮大小一致
        button_subcontainer = tk.Frame(button_container, bg="#f5f5f5")
        button_subcontainer.pack()
        
        self.create_control_panel(button_subcontainer)
    
    def create_settings_panel(self, parent):
        settings_frame = tk.Frame(parent, bg="#f5f5f5", width=300)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        settings_frame.pack_propagate(False)
        
        # 添加"练习设置"标题
        settings_title = tk.Label(settings_frame, text="练习设置", font=("Microsoft YaHei", 14, "bold"), bg="#f5f5f5")
        settings_title.pack(pady=(5, 15))
        
        # 单音选择（放在乐器类型前面）
        note_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        note_frame.pack(fill=tk.X, pady=5)
        note_label = tk.Label(note_frame, text="选择练习音符:", font=self.label_font, bg="#f5f5f5")
        note_label.pack(anchor=tk.W)
        note_options = ["C4", "D4", "E4", "F4", "G4", "A4", "B4", "C5"]
        self.note_var = tk.StringVar(value=self.current_note.get())  # 使用当前目标音符初始化
        note_dropdown = ttk.Combobox(note_frame, textvariable=self.note_var, values=note_options, font=self.label_font, state="readonly", width=15)
        note_dropdown.pack(fill=tk.X, pady=5)
        
        # 增强联动: 当用户选择新音符时完整更新UI
        def on_note_selection_change(event):
            selected_note = self.note_var.get()
            self.current_note.set(selected_note)  # 更新当前目标音符
            self.update_target_note()  # 更新目标音符显示
            # 如果在练习中，则更新pitch_detector的目标音符
            if self.practice_running and self.pitch_detector:
                self.pitch_detector.set_target_note(selected_note)
        
        note_dropdown.bind("<<ComboboxSelected>>", on_note_selection_change)
        
        # 乐器类型（放在音符选择后面）
        instrument_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        instrument_frame.pack(fill=tk.X, pady=5)
        instrument_label = tk.Label(instrument_frame, text="乐器类型:", font=self.label_font, bg="#f5f5f5")
        instrument_label.pack(anchor=tk.W)
        instrument_var = tk.StringVar(value="钢琴")
        instrument_dropdown = ttk.Combobox(instrument_frame, textvariable=instrument_var, 
                                           values=["钢琴", "小提琴", "大提琴", "长笛", "单簧管"], 
                                           font=self.label_font, state="readonly", width=15)
        instrument_dropdown.pack(fill=tk.X, pady=5)
        instrument_dropdown.bind("<<ComboboxSelected>>", lambda e: self.update_instrument_settings(instrument_var.get()))
        self.instrument_var = instrument_var

        # 音准要求
        accuracy_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        accuracy_frame.pack(fill=tk.X, pady=5)
        accuracy_label = tk.Label(accuracy_frame, text="音准要求 (±cents):", font=self.label_font, bg="#f5f5f5")
        accuracy_label.pack(anchor=tk.W)
        self.accuracy_tolerance_var = tk.IntVar(value=self.accuracy_tolerance.get())
        accuracy_scale = ttk.Scale(
            accuracy_frame,
            from_=5,
            to=30,
            orient="horizontal",
            variable=self.accuracy_tolerance_var,
            length=150
        )
        accuracy_scale.pack(anchor="w")
        self.accuracy_label = tk.Label(
            accuracy_frame,
            text=f"±{self.accuracy_tolerance_var.get()} cents",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.accuracy_label.pack(anchor="w", pady=(0, 10))
        accuracy_scale.config(command=self.update_accuracy_label)
        
        # 音量敏感度
        volume_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        volume_frame.pack(fill=tk.X, pady=5)
        volume_label = tk.Label(volume_frame, text="音量敏感度:", font=self.label_font, bg="#f5f5f5")
        volume_label.pack(anchor=tk.W)
        self.volume_threshold_var = tk.DoubleVar(value=0.03)
        volume_threshold_scale = ttk.Scale(
            volume_frame,
            from_=0.01,
            to=0.2,
            orient="horizontal",
            variable=self.volume_threshold_var,
            length=150
        )
        volume_threshold_scale.pack(fill=tk.X, pady=5)
        self.volume_threshold_label = tk.Label(
            volume_frame,
            text="较低 (0.03)",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.volume_threshold_label.pack(anchor="w", pady=(0, 5))
        volume_threshold_scale.config(command=self.update_volume_threshold_label)
        
        # 音高识别准确度
        confidence_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        confidence_frame.pack(fill=tk.X, pady=5)
        confidence_label = tk.Label(confidence_frame, text="音高识别准确度:", font=self.label_font, bg="#f5f5f5")
        confidence_label.pack(anchor=tk.W)
        self.confidence_threshold_var = tk.DoubleVar(value=0.35)
        confidence_threshold_scale = ttk.Scale(
            confidence_frame,
            from_=0.3,
            to=0.9,
            orient="horizontal",
            variable=self.confidence_threshold_var,
            length=150
        )
        confidence_threshold_scale.pack(fill=tk.X, pady=5)
        self.confidence_threshold_label = tk.Label(
            confidence_frame,
            text="宽松 (0.35)",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.confidence_threshold_label.pack(anchor="w", pady=(0, 5))
        confidence_threshold_scale.config(command=self.update_confidence_threshold_label)
        
        # 成功判定帧数
        frames_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        frames_frame.pack(fill=tk.X, pady=5)
        frames_label = tk.Label(frames_frame, text="成功判定帧数:", font=self.label_font, bg="#f5f5f5")
        frames_label.pack(anchor=tk.W)
        frames_scale = ttk.Scale(
            frames_frame,
            from_=1,
            to=10,
            orient="horizontal",
            variable=self.success_frames_needed,
            length=150
        )
        frames_scale.pack(fill=tk.X, pady=5)
        self.frames_label = tk.Label(
            frames_frame,
            text=f"连续 {self.success_frames_needed.get()} 帧",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.frames_label.pack(anchor="w", pady=(0, 5))
        frames_scale.config(command=self.update_frames_label)
        
        # 判断模式
        judgement_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        judgement_frame.pack(fill=tk.X, pady=5)
        judgement_label = tk.Label(judgement_frame, text="判断模式:", font=self.label_font, bg="#f5f5f5")
        judgement_label.pack(anchor=tk.W, pady=(5, 0))
        
        judgement_mode_frame = tk.Frame(judgement_frame, bg="#f5f5f5")
        judgement_mode_frame.pack(anchor="w", fill=tk.X, pady=5)
        tk.Radiobutton(
            judgement_mode_frame,
            text="按帧数",
            variable=self.judgement_mode,
            value="frames",
            font=self.label_font,
            bg="#f5f5f5",
            command=self.update_judgement_settings_ui
        ).pack(side=tk.LEFT)
        tk.Radiobutton(
            judgement_mode_frame,
            text="按时长",
            variable=self.judgement_mode,
            value="duration",
            font=self.label_font,
            bg="#f5f5f5",
            command=self.update_judgement_settings_ui
        ).pack(side=tk.LEFT, padx=(10,0))
        
        # 目标演奏时长设置 (使用Frame包裹，方便整体显隐)
        duration_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        duration_frame.pack(fill=tk.X, pady=5)
        
        self.duration_setting_frame = tk.Frame(duration_frame, bg="#f5f5f5")
        # self.duration_setting_frame不在此处pack，由update_judgement_settings_ui控制

        tk.Label(
            self.duration_setting_frame,
            text="目标时长(秒):",
            font=self.label_font,
            bg="#f5f5f5"
        ).pack(side=tk.LEFT, pady=(5,0))

        self.duration_scale = ttk.Scale(
            self.duration_setting_frame,
            from_=1,
            to=10,
            orient="horizontal",
            variable=self.target_duration,
            length=100,
            command=self.update_target_duration_label
        )
        self.duration_scale.pack(side=tk.LEFT, anchor="w", padx=(5,0), pady=(5,0))
        
        self.duration_label = tk.Label(
            self.duration_setting_frame,
            text=f"{self.target_duration.get():.1f} 秒",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.duration_label.pack(side=tk.LEFT, anchor="w", padx=(5,0), pady=(5,0))
        
        # 初始化UI状态
        self.update_judgement_settings_ui()
    
    def create_practice_area(self, parent):
        """创建右侧练习区域"""
        # 目标音符区
        note_frame = tk.LabelFrame(
            parent,
            text="五线谱目标音符区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        note_frame.pack(fill="x", pady=(0, 10))
        
        self.target_note_label = tk.Label(
            note_frame,
            text=self.current_note.get(),
            font=("Microsoft YaHei", 60, "bold"),
            bg="#ffffff",
            fg="#3f51b5"
        )
        self.target_note_label.pack(pady=10)
        
        # 钢琴键盘对应区
        keyboard_frame = tk.LabelFrame(
            parent,
            text="钢琴键盘对应区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        keyboard_frame.pack(fill="x", pady=(0, 10))
        
        # 简单的键盘显示
        self.create_piano_keyboard(keyboard_frame)
        
        # 音高检测反馈区
        feedback_frame = tk.LabelFrame(
            parent,
            text="音高检测反馈区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        feedback_frame.pack(fill="both", expand=True)
        
        # 创建音高显示图表
        self.create_pitch_chart(feedback_frame)
        
        # 音量显示区域
        volume_frame = tk.Frame(feedback_frame, bg="#ffffff")
        volume_frame.pack(fill="x", pady=(5, 10))
        
        tk.Label(
            volume_frame,
            text="音量:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        # 创建音量条容器
        volume_bar_frame = tk.Frame(volume_frame, bg="#ffffff", width=200, height=20)
        volume_bar_frame.pack(side=tk.LEFT, padx=5)
        volume_bar_frame.pack_propagate(False)  # 固定大小
        
        # 创建音量条
        self.volume_bar = tk.Canvas(volume_bar_frame, width=200, height=20, bg="#f0f0f0", highlightthickness=0)
        self.volume_bar.pack(fill="both")
        
        # 当前音高信息显示
        info_frame = tk.Frame(feedback_frame, bg="#ffffff")
        info_frame.pack(fill="x", pady=10)
        
        tk.Label(
            info_frame,
            text="目标音:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.target_info_label = tk.Label(
            info_frame,
            text=self.current_note.get(),
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff"
        )
        self.target_info_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            info_frame,
            text="当前音:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.current_pitch_label = tk.Label(
            info_frame,
            text="-- Hz",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.current_pitch_label.pack(side=tk.LEFT)
        
        # 第二行信息
        info_frame2 = tk.Frame(feedback_frame, bg="#ffffff")
        info_frame2.pack(fill="x")
        
        tk.Label(
            info_frame2,
            text="偏差:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.deviation_label = tk.Label(
            info_frame2,
            text="-- cents",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.deviation_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            info_frame2,
            text="达标:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.success_frames_label = tk.Label(
            info_frame2,
            text=f"0/{self.success_frames_needed.get()}帧",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.success_frames_label.pack(side=tk.LEFT)
        
        # 第三行 - 状态信息显示
        status_frame = tk.Frame(feedback_frame, bg="#ffffff")
        status_frame.pack(fill="x", pady=(10, 0))
        
        tk.Label(
            status_frame,
            text="状态:",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.status_text_label = tk.Label(
            status_frame,
            text="等待开始",
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff",
            fg="#2196f3"
        )
        self.status_text_label.pack(side=tk.LEFT)
    
    def create_piano_keyboard(self, parent):
        """创建钢琴键盘区域，自动支持C5显示"""
        # 先清空parent下所有控件，防止重复
        for widget in parent.winfo_children():
            widget.destroy()
        # 创建画布，宽度要足够容纳C4~C5
        keyboard_canvas = tk.Canvas(parent, height=80, width=380, bg="white")
        keyboard_canvas.pack(fill="x", pady=10)
        self.keyboard_canvas = keyboard_canvas
        self.draw_piano_keys()
    
    def draw_piano_keys(self):
        """在画布上绘制钢琴键盘，支持C4~B4和C5"""
        if not hasattr(self, 'keyboard_canvas'):
            return

        self.keyboard_canvas.delete("all")

        key_width = 40
        key_height = 60
        start_x = 10
        y1 = 10
        y2 = y1 + key_height

        # 白键列表，最后一个C是C5
        white_keys = [("C", 4), ("D", 4), ("E", 4), ("F", 4), ("G", 4), ("A", 4), ("B", 4), ("C", 5)]

        # 绘制白键
        for i, (key, octave) in enumerate(white_keys):
            x1 = start_x + i * key_width
            x2 = x1 + key_width
            note_name = f"{key}{octave}"

            # 默认颜色
            fill_color = "#e3f2fd"
            # 高亮当前目标音符
            if note_name == self.current_note.get():
                fill_color = "#2196f3"

            self.keyboard_canvas.create_rectangle(x1, y1, x2, y2, fill=fill_color, outline="black")
            self.keyboard_canvas.create_text(x1 + key_width/2, y2 - 15, text=note_name)

        # 黑键信息（音名, 八度, 相对白键索引）
        black_keys = [
            ("C#", 4, 0), ("D#", 4, 1), ("F#", 4, 3), ("G#", 4, 4), ("A#", 4, 5)
        ]
        black_key_width = key_width * 0.6
        black_key_height = key_height * 0.6

        # 绘制黑键
        for key, octave, idx in black_keys:
            x1 = start_x + (idx + 1) * key_width - black_key_width / 2
            x2 = x1 + black_key_width
            y1b = y1
            y2b = y1 + black_key_height
            self.keyboard_canvas.create_rectangle(x1, y1b, x2, y2b, fill="black", outline="black")

        # 画布宽度要足够
        self.keyboard_canvas.config(width=start_x + len(white_keys) * key_width + 20)
    
    def create_pitch_chart(self, parent):
        """创建音高显示图表"""
        # 创建图表框架
        chart_frame = tk.Frame(parent, bg="#ffffff")
        chart_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 使用matplotlib创建图表
        self.fig, self.ax = plt.subplots(figsize=(5, 3))
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 初始化图表
        self.init_pitch_chart()
    
    def init_pitch_chart(self):
        """初始化音高图表"""
        self.ax.clear()
        
        # 设置Y轴范围（音分偏差）
        y_range = 50  # 上下各显示50音分
        self.ax.set_ylim(-y_range, y_range)
        
        # 添加三色区域
        self.ax.axhspan(-self.accuracy_tolerance.get(), self.accuracy_tolerance.get(), color='#4caf50', alpha=0.3)  # 绿色区域（达标）
        self.ax.axhspan(self.accuracy_tolerance.get(), y_range, color='#f44336', alpha=0.3)  # 红色区域（偏高）
        self.ax.axhspan(-y_range, -self.accuracy_tolerance.get(), color='#2196f3', alpha=0.3)  # 蓝色区域（偏低）
        
        # 添加中心线和刻度
        self.ax.axhline(y=0, color='black', linestyle='-', linewidth=1)
        self.ax.axhline(y=self.accuracy_tolerance.get(), color='black', linestyle='--', linewidth=0.5)
        self.ax.axhline(y=-self.accuracy_tolerance.get(), color='black', linestyle='--', linewidth=0.5)
        
        # 设置标签
        self.ax.set_ylabel('音分偏差 (cents)')
        self.ax.set_title(f'目标音符: {self.current_note.get()}')
        
        # 设置X轴（改为帧数）
        self.ax.set_xlim(0, 100)
        self.ax.set_xlabel('帧数 (音频采样点)')
        
        # 绘制初始数据点（空）
        self.pitch_line, = self.ax.plot([], [], 'o-', markersize=3)
        
        # 重绘
        self.fig.tight_layout()
        self.canvas.draw()
    
    def create_control_panel(self, parent):
        """创建底部控制面板"""
        # 控制按钮
        self.start_button = tk.Button(
            parent,
            text="开始练习",
            font=self.button_font,
            bg="#4caf50",
            fg="white",
            padx=25,    # 增加内边距
            pady=12,    # 增加内边距
            width=10,
            command=self.start_practice
        )
        self.start_button.pack(side=tk.LEFT, padx=10)
        
        self.pause_button = tk.Button(
            parent,
            text="暂停",
            font=self.button_font,
            bg="#ff9800",
            fg="white",
            padx=25,    # 增加内边距
            pady=12,    # 增加内边距
            width=8,
            state="disabled",
            command=self.pause_practice
        )
        self.pause_button.pack(side=tk.LEFT, padx=10)
        
        self.reset_button = tk.Button(
            parent,
            text="重置",
            font=self.button_font,
            bg="#f44336",
            fg="white",
            padx=25,    # 增加内边距
            pady=12,    # 增加内边距
            width=8,
            state="disabled",
            command=self.reset_practice
        )
        self.reset_button.pack(side=tk.LEFT, padx=10)
        
        self.next_button = tk.Button(
            parent,
            text="下一个音符",
            font=self.button_font,
            bg="#2196f3",
            fg="white",
            padx=25,    # 增加内边距
            pady=12,    # 增加内边距
            width=12,
            command=self.next_note
        )
        self.next_button.pack(side=tk.LEFT, padx=10)
    
    def update_accuracy_label(self, value=None):
        """更新音准要求标签"""
        self.accuracy_label.config(text=f"±{self.accuracy_tolerance.get()} cents")
        # 如果图表已初始化，也更新图表
        if hasattr(self, 'ax'):
            self.init_pitch_chart()
    
    def update_frames_label(self, value=None):
        """更新成功帧数标签"""
        self.frames_label.config(text=f"连续 {self.success_frames_needed.get()} 帧")
        self.success_frames_label.config(text=f"0/{self.success_frames_needed.get()}帧")
    
    def update_volume_threshold_label(self, value):
        """更新音量敏感度标签"""
        threshold = float(value)
        if threshold < 0.03:
            sensitivity = "很高"
        elif threshold < 0.07:
            sensitivity = "较低"
        elif threshold < 0.12:
            sensitivity = "中等"
        else:
            sensitivity = "较高"
        self.volume_threshold_label.config(text=f"{sensitivity} ({threshold:.2f})")
        # 如果正在练习，实时更新检测器参数
        if self.practice_running and self.pitch_detector:
            self.pitch_detector.set_amplitude_threshold(threshold)

    def update_confidence_threshold_label(self, value):
        """更新音高识别准确度标签"""
        threshold = float(value)
        if threshold < 0.4:
            level = "宽松"
        elif threshold < 0.6:
            level = "较宽松"
        elif threshold < 0.75:
            level = "标准"
        elif threshold < 0.85:
            level = "严格"
        else:
            level = "很严格"
        self.confidence_threshold_label.config(text=f"{level} ({threshold:.2f})")
        # 如果正在练习，实时更新检测器参数
        if self.practice_running and self.pitch_detector:
            self.pitch_detector.set_confidence_threshold(threshold)
    
    def update_target_note(self, event=None):
        """更新目标音符"""
        note = self.current_note.get()
        self.target_note_label.config(text=note)
        self.target_info_label.config(text=note)
        
        # 更新图表
        if hasattr(self, 'ax'):
            self.init_pitch_chart()
        
        # 更新键盘显示 - 直接在原有画布上重绘
        if hasattr(self, 'keyboard_canvas'):
            self.draw_piano_keys()
    
    def process_audio_queue(self):
        """处理音频数据队列"""
        try:
            while not self.audio_queue.empty():
                data = self.audio_queue.get_nowait()
                if data is not None:
                    timestamp, frequency, note_name, confidence, cents_deviation, amplitude = data
                    
                    # 如果时长模式已经成功，则不再处理音频数据
                    if self.judgement_mode.get() == "duration" and self.duration_success:
                        continue
                    
                    # 更新音量显示 (如果有)
                    if hasattr(self, 'volume_bar'):
                        self.update_volume_display(amplitude)
                    
                    # 如果检测到有效音高 (frequency可能为None)
                    if frequency is not None and frequency > 0:
                        # 更新UI，传递时间戳。置信度等检查在update_pitch_ui中进行
                        self.update_pitch_ui(timestamp, frequency, note_name, confidence, cents_deviation, amplitude)
        except queue.Empty: # 明确捕获队列为空的异常
            pass # 队列为空是正常情况，无需记录错误
        except Exception as e:
            logger.error(f"处理音频数据出错: {e}")
        finally:
            # 继续定时器
            self.root.after(10, self.process_audio_queue) # 保持10ms轮询
    
    def pitch_callback(self, frequency, note_name, confidence, cents_deviation, amplitude):
        """音高检测回调函数"""
        if not self.practice_running:
            return
        
        current_time = time.time() # 获取当前时间戳
        # 将数据放入队列，包含时间戳
        self.audio_queue.put((current_time, frequency, note_name, confidence, cents_deviation, amplitude))
    
    def update_pitch_ui(self, timestamp, frequency, note_name, confidence, cents_deviation, amplitude=None):
        """更新音高相关UI"""
        # 如果时长模式已经成功完成，则不再处理新的评估
        if self.judgement_mode.get() == "duration" and self.duration_success:
            # 保持当前状态，只更新图表
            self.update_pitch_chart()
            return
            
        # 更新当前音高显示
        self.current_pitch_label.config(
            text=f"{frequency:.1f} Hz ({note_name})",
            fg="#3f51b5" if abs(cents_deviation) <= self.accuracy_tolerance.get() else "#f44336"
        )
        
        # 更新偏差显示
        self.deviation_label.config(
            text=f"{cents_deviation:+.1f} cents",
            fg="#3f51b5" if abs(cents_deviation) <= self.accuracy_tolerance.get() else "#f44336"
        )
        
        # 首先检查音量是否足够 (amplitude 可能是 None，如果调用时未提供)
        if amplitude is not None and amplitude < self.volume_threshold_var.get():
            self.status_text_label.config(text="音量过低", fg="#ff9800")
            if self.judgement_mode.get() == "duration" and self.practice_start_time is not None:
                # 音量过低，对于时长模式，重置计时，避免无效计时累积
                self.practice_start_time = timestamp # 使用当前音高事件的时间戳作为新的开始
                self.duration_pitch_data = []
                if hasattr(self, 'success_frames_label'):
                     self.success_frames_label.config(text=f"0.0/{self.target_duration.get():.1f}秒")
            self.update_pitch_chart() # 即使音量低也更新图表
            return # 音过低，不进行后续音准判断量

        # 检查音高检测置信度
        if confidence < self.confidence_threshold_var.get():
            self.status_text_label.config(text="识别度低", fg="#ff9800")
            if self.judgement_mode.get() == "duration" and self.practice_start_time is not None:
                # 识别度低，也重置时长模式的计时
                self.practice_start_time = timestamp 
                self.duration_pitch_data = []
                if hasattr(self, 'success_frames_label'):
                     self.success_frames_label.config(text=f"0.0/{self.target_duration.get():.1f}秒")
            self.update_pitch_chart()
            return # 置信度不足，不进行后续判断
        
            # 将音准容差阈值定义在分支判断之前，使其在整个后续逻辑中可用
            threshold = self.accuracy_tolerance.get()
            target_note_name_only = self.current_note.get().rstrip("0123456789")
            detected_note_name_only = note_name.rstrip("0123456789")

        # 将有效数据存入时长模式的数据列表 (在音量和置信度都达标后)
        if self.judgement_mode.get() == "duration" and self.practice_start_time is not None:
            self.duration_pitch_data.append({'ts': timestamp, 'dev': cents_deviation, 'note': note_name, 'amp': amplitude, 'conf': confidence})
            # 清理过老的数据: 例如，只保留比 practice_start_time 新的数据，或限定最大长度
            # self.duration_pitch_data = [d for d in self.duration_pitch_data if d['ts'] >= self.practice_start_time - 1] # 保留略早一点的数据以防边缘情况
            if len(self.duration_pitch_data) > 200: # 防止列表过长，假设帧率较高
                self.duration_pitch_data = self.duration_pitch_data[-200:]
            
            # 添加时长模式的诊断日志
            logger.info(f"时长模式: 当前时间={timestamp:.2f}, 开始时间={self.practice_start_time:.2f}, " +
                       f"已经过时间={(timestamp-self.practice_start_time):.2f}秒, 目标时长={self.target_duration.get():.1f}秒")

        # 将音准容差阈值定义在分支判断之前，使其在整个后续逻辑中可用
        threshold = self.accuracy_tolerance.get() # <--- 确保这行在这里
        target_note_name_only = self.current_note.get().rstrip("0123456789")
        detected_note_name_only = note_name.rstrip("0123456789")
        mode = self.judgement_mode.get() # 获取判断模式
        
        # 添加模式切换诊断日志
        logger.info(f"当前判断模式: {mode}, 音符: {note_name}, 偏差: {cents_deviation:.2f}cents, 阈值: ±{threshold}cents")

        if self.judgement_mode.get() == "frames": # frames 变量名修改为 mode
            # --- 按帧数判断逻辑 ---
            if abs(cents_deviation) <= threshold and target_note_name_only == detected_note_name_only:
                # 音准正确且音符匹配
                self.consecutive_success_frames += 1
                
                # 更新状态显示
                self.status_text_label.config(text=f"音准正确 {self.consecutive_success_frames}/{self.success_frames_needed.get()}", fg="#4caf50")
                
                # 如果连续成功帧数达到要求且尚未显示成功对话框
                if self.consecutive_success_frames >= self.success_frames_needed.get() and not self.frames_success:
                    self.frames_success = True  # 设置标志防止重复弹窗
                    self.status_text_label.config(text="达标成功! 练习结束", fg="#4caf50")
                    self.success_count += 1
                    self.pause_practice()
                    self.show_success_dialog()
                    logger.info("帧数模式评估结果: 成功!")
            else:
                # 音准不正确或音符不匹配，重置连续成功帧数
                self.consecutive_success_frames = 0
                
                # 更新状态显示
                if target_note_name_only != detected_note_name_only:
                    self.status_text_label.config(text="音符错误", fg="#f44336")
                elif cents_deviation > 0:
                    self.status_text_label.config(text="音高偏高", fg="#f44336")
                else:
                    self.status_text_label.config(text="音高偏低", fg="#f44336")
            
            # 更新帧数显示
            self.success_frames_label.config(
                text=f"{self.consecutive_success_frames}/{self.success_frames_needed.get()}帧",
                fg="#4caf50" if self.consecutive_success_frames > 0 else "#f44336"
            )
        elif mode == "duration":
    # --- 按时长判断逻辑 ---
            if self.practice_start_time is None: 
                self.status_text_label.config(text="点击开始(时长)", fg="#2196f3") # 例如这样提示
                # self.update_pitch_chart() # 图表此时应为空或由reset处理
                return # 如果计时未开始，则不处理当前这一帧的评估

            elapsed_time = timestamp - self.practice_start_time
            
            if hasattr(self, 'success_frames_label'):
                self.success_frames_label.config(
                    text=f"{elapsed_time:.1f}/{self.target_duration.get():.1f}秒", 
                    fg="#2196f3" # 计时过程中的颜色
                )

            if elapsed_time >= self.target_duration.get():
                all_correct_in_duration = True
                start_window_time = self.practice_start_time
                end_window_time = self.practice_start_time + self.target_duration.get()
                
                # 添加时长达标日志
                logger.info(f"时长已达标: 目标={self.target_duration.get():.1f}秒, 实际={(elapsed_time):.2f}秒")
                
                # 达到目标时间后，更新显示为完整时间
                if hasattr(self, 'success_frames_label'):
                    self.success_frames_label.config(
                        text=f"{self.target_duration.get():.1f}/{self.target_duration.get():.1f}秒", 
                        fg="#4caf50"  # 成功时使用绿色
                    )
                
                relevant_data = [d for d in self.duration_pitch_data if d['ts'] >= start_window_time and d['ts'] <= end_window_time]
                
                logger.info(f"评估数据点数量: {len(relevant_data)}")

                if not relevant_data:
                    all_correct_in_duration = False 
                    logger.warning("没有有效数据点进行评估!")
                else:
                    for data_point in relevant_data:
                        dp_note_name_only = data_point['note'].rstrip("0123456789")
                        if abs(data_point['dev']) > threshold or \
                           dp_note_name_only != target_note_name_only:
                            all_correct_in_duration = False
                            logger.info(f"评估失败点: 音符={data_point['note']}, 偏差={data_point['dev']:.2f}cents")
                            break
                
                # 暂存本次评估的数据，用于可能的弹窗显示或其他分析
                # 无论成功失败，都存一下，方便调试或未来扩展
                setattr(self, '_last_evaluated_duration_data', relevant_data.copy())

                if all_correct_in_duration:
                    # 成功时设置标志并记录时长
                    self.duration_success = True
                    self.final_duration = self.target_duration.get()
                    
                    self.status_text_label.config(text=f"{self.target_duration.get():.0f}秒内音准稳定! 练习结束", fg="#4caf50")
                    logger.info("时长模式评估结果: 成功!")
                    logger.info(f"更新状态文本为: {self.target_duration.get():.0f}秒内音准稳定! 练习结束")
                    
                    # 记录并增加成功次数
                    self.success_count += 1
                    
                    # 先调用pause_practice停止练习，这会清空音频队列
                    logger.info("时长模式成功，暂停练习")
                    self.pause_practice() 
                    
                    # 最后才调用显示成功对话框，避免重复触发
                    logger.info("显示时长模式成功对话框")
                    self.show_success_dialog()
                else:
                    self.status_text_label.config(text=f"{self.target_duration.get():.0f}秒内音准未达标", fg="#f44336")
                    logger.info("时长模式评估结果: 失败!")
                    logger.info(f"更新状态文本为: {self.target_duration.get():.0f}秒内音准未达标")
                    if not self.in_grace_period: self.attempt_count += 1
                
                # 评估完成后，重置计时起点为当前评估窗口的结束点，并清理旧数据
                if not self.duration_success:  # 只有在未成功时才重置
                    self.practice_start_time = timestamp
                    self.duration_pitch_data = [d for d in self.duration_pitch_data if d['ts'] > end_window_time] 
                    if hasattr(self, 'success_frames_label'): 
                        self.success_frames_label.config(text=f"0.0/{self.target_duration.get():.1f}秒")
            else:
                # 未达到目标时长，提供实时反馈
                # 如果已经成功，不更新时间显示，保持最终时间
                if self.duration_success:
                    # 已成功完成，显示最终时间
                    if hasattr(self, 'success_frames_label'):
                        self.success_frames_label.config(
                            text=f"{self.final_duration:.1f}/{self.target_duration.get():.1f}秒", 
                            fg="#4caf50"  # 使用绿色表示成功
                        )
                    # 保持成功状态文本
                    self.status_text_label.config(text=f"{self.target_duration.get():.0f}秒内音准稳定! 练习结束", fg="#4caf50")
                else:
                    # 未成功，显示实时时间
                    if hasattr(self, 'success_frames_label'):
                        self.success_frames_label.config(
                            text=f"{elapsed_time:.1f}/{self.target_duration.get():.1f}秒", 
                            fg="#2196f3" # 计时过程中的颜色
                        )
                    
                    if target_note_name_only != detected_note_name_only:
                         self.status_text_label.config(text="音符错误(时长)", fg="#f44336")
                    elif abs(cents_deviation) > threshold:
                        self.status_text_label.config(text="音高偏高(时长)" if cents_deviation > 0 else "音高偏低(时长)", fg="#f44336")
                    else:
                        self.status_text_label.config(text="保持住...", fg="#2196f3")
                
            # 移除了重复出现的duration代码块并更新图表
            self.update_pitch_chart()
            
            # 更新图表数据
            self.time_data.append(timestamp)
            self.cents_data.append(cents_deviation)
            
            # 如果传入了音量数据，则保存
            if amplitude is not None:
                self.amplitude_data.append(amplitude)
            # 更新音量显示
            self.update_volume_display(amplitude)
            
            # 保持数据长度在100以内
            if len(self.time_data) > 100:
                self.time_data = self.time_data[-100:]
                self.cents_data = self.cents_data[-100:]
                if len(self.amplitude_data) > 100:
                    self.amplitude_data = self.amplitude_data[-100:]
                
            return  # 时长模式单独处理完成后返回
        
        # 下面是通用的图表数据更新代码，只有在frames模式下运行到这里
        # 更新图表数据
        self.time_data.append(timestamp)
        self.cents_data.append(cents_deviation)
        
        # 如果传入了音量数据，则保存
        if amplitude is not None:
            self.amplitude_data.append(amplitude)
            # 更新音量显示
            self.update_volume_display(amplitude)
        
        # 保持数据长度在100以内
        if len(self.time_data) > 100:
            self.time_data = self.time_data[-100:]
            self.cents_data = self.cents_data[-100:]
            if len(self.amplitude_data) > 100:
                self.amplitude_data = self.amplitude_data[-100:]
        
        # 检查音准是否在容差范围内
        if abs(cents_deviation) <= self.accuracy_tolerance.get():
            # 音准在要求范围内
            
            # 检查音符是否正确
            target_note_name = self.current_note.get().rstrip("0123456789")  # 去除数字获取音符名称
            detected_note_name = note_name.rstrip("0123456789")  # 去除数字获取音符名称
            
            if target_note_name == detected_note_name:
                # 音准在要求范围内且音符正确
                self.consecutive_success_frames += 1
                required_frames = self.success_frames_needed.get()
                
                # 重置错误音符帧数和鼓励弹窗状态
                self.wrong_note_frames = 0
                self.encourage_shown = False
                
                # 更新成功帧数显示
                self.success_frames_label.config(
                    text=f"{self.consecutive_success_frames}/{required_frames}帧",
                    fg="#4caf50"
                )
                
                # 实时状态显示
                self.status_text_label.config(text="音准正确", fg="#4caf50")
                
                # 如果连续成功帧数达到要求且尚未显示成功对话框
                if self.consecutive_success_frames >= self.success_frames_needed.get() and not self.frames_success:
                    self.frames_success = True  # 设置标志防止重复弹窗
                    self.status_text_label.config(text="达标成功! 练习结束", fg="#4caf50")
                    self.success_count += 1
                    self.pause_practice()
                    self.show_success_dialog()
                    logger.info("帧数模式评估结果: 成功! (位置1)")
            else:
                # 音准正确但音符不匹配
                self.consecutive_success_frames = 0
                self.attempt_count += 1
                
                # 只有不在缓冲期时才累加错误音符计数
                if not self.in_grace_period:
                    self.wrong_note_frames += 1
                    # 达到连续错误音符阈值且未弹过鼓励弹窗时弹窗鼓励
                    if self.wrong_note_frames >= 3 and not self.encourage_shown:
                        messagebox.showinfo("鼓励", "再试一次，加油！")
                        self.wrong_note_frames = 0
                        self.encourage_shown = True
                
                # 更新成功帧数显示
                self.success_frames_label.config(
                    text=f"0/{self.success_frames_needed.get()}帧",
                    fg="#f44336"
                )
                # 实时状态显示
                self.status_text_label.config(text="音符错误", fg="#f44336")
        else:
            # 音准不在要求范围内
            self.consecutive_success_frames = 0
            self.attempt_count += 1
            
            # 只有不在缓冲期且音量足够时才累加错误音符计数
            if not self.in_grace_period and amplitude >= self.volume_threshold_var.get():
                self.wrong_note_frames += 1
                # 达到连续错误音符阈值且未弹过鼓励弹窗时弹窗鼓励
                if self.wrong_note_frames >= 3 and not self.encourage_shown:
                    messagebox.showinfo("鼓励", "再试一次，加油！")
                    self.wrong_note_frames = 0
                    self.encourage_shown = True
            
            # 更新成功帧数显示
            self.success_frames_label.config(
                text=f"0/{self.success_frames_needed.get()}帧",
                fg="#f44336"
            )
            
            # 实时状态显示
            if cents_deviation > 0:
                self.status_text_label.config(text="音高偏高", fg="#f44336")
            else:
                self.status_text_label.config(text="音高偏低", fg="#f44336")
        
        # 更新图表
        self.update_pitch_chart()
    
    def update_volume_display(self, amplitude):
        """更新音量显示"""
        # 保存音量数据
        if not hasattr(self, 'amplitude_data'):
            self.amplitude_data = []
        self.amplitude_data.append(amplitude)
        if len(self.amplitude_data) > 100:
            self.amplitude_data = self.amplitude_data[-100:]

        # 清除当前音量条
        self.volume_bar.delete("all")

        # 计算音量条宽度 (最大300像素)
        bar_width = min(300, int(amplitude * 1500))

        # 根据音量值确定颜色
        if amplitude < 0.3:
            color = "#2196f3"  # 蓝色 - 音量低
        elif amplitude < 0.7:
            color = "#4caf50"  # 绿色 - 音量适中
        else:
            color = "#f44336"  # 红色 - 音量高

        # 绘制音量条
        self.volume_bar.create_rectangle(0, 0, bar_width, 20, fill=color, outline="")

        # 标记刻度
        for i in range(1, 16):
            x = i * 20
            height = 5 if i % 5 == 0 else 3
            self.volume_bar.create_line(x, 20, x, 20 - height, fill="#aaaaaa")

        # 绘制音量阈值指示线
        threshold_pos = min(300, int(self.volume_threshold_var.get() * 1500))
        self.volume_bar.create_line(threshold_pos, 0, threshold_pos, 20, fill="#ff9800", width=2)
    
    def start_practice(self):
        """开始音准练习"""
        # 检查音高检测器是否可用
        if self.pitch_detector is None:
            messagebox.showerror("错误", "音高检测器不可用，无法开始练习")
            return
        
        # 更新按钮状态
        self.start_button.config(state="disabled")
        self.pause_button.config(state="normal")
        self.reset_button.config(state="normal")
        
        # 设置练习状态
        self.practice_running = True
        
        # 重置统计数据
        self.consecutive_success_frames = 0
        self.success_count = 0
        self.attempt_count = 0
        self.time_data = []
        self.cents_data = []
        self.amplitude_data = []
        
        # 重置错误音符相关状态
        self.wrong_note_frames = 0
        self.encourage_shown = False
        
        # 设置缓冲期 - 开始练习后短暂时间内不计入错误
        self.in_grace_period = True
        self.root.after(2000, lambda: setattr(self, 'in_grace_period', False))  # 2秒后结束缓冲期
        
        # 新增：根据模式初始化
        if self.judgement_mode.get() == "duration":
            self.practice_start_time = time.time() # 开始计时
            self.duration_pitch_data = []
            self.duration_success = False  # 重置成功标志
            self.final_duration = 0.0  # 重置最终时长
            if hasattr(self, 'success_frames_label'): # 确保UI元素存在
                self.success_frames_label.config(text=f"0.0/{self.target_duration.get():.1f}秒", fg="#2196f3")
            self.status_text_label.config(text="开始时长练习模式", fg="#2196f3")
            logger.info(f"启动时长模式练习: 目标时长={self.target_duration.get():.1f}秒, 开始时间={self.practice_start_time:.2f}")
        else: # frames mode
            self.practice_start_time = None # 确保时长计时器不启动
            self.consecutive_success_frames = 0 # 此处已在上面重置过
            self.frames_success = False  # 重置帧数模式成功标志
            if hasattr(self, 'success_frames_label'): # 确保UI元素存在
                self.success_frames_label.config(text=f"0/{self.success_frames_needed.get()}帧", fg="#f44336")
            self.status_text_label.config(text="开始帧数练习模式", fg="#2196f3")
            logger.info(f"启动帧数模式练习: 目标帧数={self.success_frames_needed.get()}帧")
        
        # 设置目标音符
        target_note = self.current_note.get()
        self.pitch_detector.set_target_note(target_note)
        
        # 设置乐器类型
        instrument_name = self.instrument_type.get()
        instrument_map = {
            "默认": "default",
            "钢琴": "piano",
            "弦乐器": "strings",
            "大提琴": "cello",
            "小提琴": "violin",
            "长笛": "flute"
        }
        instrument_type = instrument_map.get(instrument_name, "default")
        self.pitch_detector.set_instrument_type(instrument_type)
        
        # 设置音高检测参数
        self.pitch_detector.set_accuracy_threshold(self.accuracy_tolerance.get())
        self.pitch_detector.set_amplitude_threshold(self.volume_threshold_var.get())
        self.pitch_detector.set_confidence_threshold(self.confidence_threshold_var.get())
        
        # 启动音高检测
        try:
            self.pitch_detector.start(callback=self.pitch_callback)
            logger.info(f"开始练习: 目标音符={target_note}, 乐器类型={instrument_name}")
        except Exception as e:
            messagebox.showerror("错误", f"启动音高检测失败: {e}")
            self.reset_practice()
            logger.error(f"启动音高检测失败: {e}")
    
    def pause_practice(self):
        """暂停音准练习"""
        # 更新按钮状态
        self.start_button.config(state="normal")
        self.pause_button.config(state="disabled")
        
        # 暂停音高检测
        if self.pitch_detector and self.practice_running:
            self.pitch_detector.stop()
        
        # 设置练习状态
        self.practice_running = False
        
        # 清空音频队列，防止暂停后继续处理旧数据
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break
        
        # 不重置状态文本，保留当前状态信息
        logger.info("暂停练习，保留当前状态显示")
    
    def reset_practice(self):
        """重置音准练习"""
        self.start_button.config(state="normal")
        self.pause_button.config(state="disabled")
        self.reset_button.config(state="disabled")
        if self.pitch_detector and self.practice_running:
            self.pitch_detector.stop()
        self.practice_running = False
        self.init_pitch_chart()
        self.current_pitch_label.config(text="-- Hz")
        self.deviation_label.config(text="-- cents")
        self.consecutive_success_frames = 0
        self.success_count = 0
        self.attempt_count = 0
        self.time_data = []
        self.cents_data = []
        self.amplitude_data = []
        self.wrong_note_frames = 0
        self.encourage_shown = False
        self.in_grace_period = False
        self.practice_start_time = None
        self.duration_pitch_data = []
        self.duration_success = False
        self.final_duration = 0.0
        self.frames_success = False  # 保证重置时清除成功标志
        if hasattr(self, 'success_frames_label'):
            if self.judgement_mode.get() == "duration":
                self.success_frames_label.config(text=f"0.0/{self.target_duration.get():.1f}秒")
                logger.info("重置时长模式练习状态")
            else:
                self.success_frames_label.config(text=f"0/{self.success_frames_needed.get()}帧")
                logger.info("重置帧数模式练习状态")
        self.status_text_label.config(text="等待开始", fg="#2196f3")
        logger.info("练习已重置，等待开始新的练习")
    
    def next_note(self, start=True):
        """切换到下一个音符"""
        note_options = ["C4", "D4", "E4", "F4", "G4", "A4", "B4", "C5"]
        try:
            current_index = note_options.index(self.current_note.get())
            next_index = (current_index + 1) % len(note_options)
            self.current_note.set(note_options[next_index])
            was_running = self.practice_running
            self.reset_practice()
            self.target_note_label.config(text=self.current_note.get())
            self.target_info_label.config(text=self.current_note.get())
            if hasattr(self, 'ax'):
                self.init_pitch_chart()
            if hasattr(self, 'keyboard_canvas'):
                self.keyboard_canvas.delete("all")
                self.draw_piano_keys()
            if start:
                self.start_practice()
        except ValueError:
            self.current_note.set("C4")
            self.update_target_note()
    
    def open_settings(self):
        """打开设置对话框"""
        messagebox.showinfo("功能开发中", "设置功能正在开发中，敬请期待！")
    
    def show_success_dialog(self):
        """显示成功对话框"""
        success_dialog = tk.Toplevel(self.root)
        success_dialog.title("成功")
        success_dialog.geometry("300x200")
        success_dialog.configure(bg="#ffffff")
        success_dialog.transient(self.root)
        success_dialog.grab_set()
        success_dialog.update_idletasks()
        width = success_dialog.winfo_width()
        height = success_dialog.winfo_height()
        x = (success_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (success_dialog.winfo_screenheight() // 2) - (height // 2)
        success_dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        tk.Label(
            success_dialog,
            text="🎉 太棒了！",
            font=("Microsoft YaHei", 18, "bold"),
            bg="#ffffff"
        ).pack(pady=(20, 10))
        tk.Label(
            success_dialog,
            text=f"您已成功达标{self.current_note.get()}音符！",
            font=self.label_font,
            bg="#ffffff"
        ).pack(pady=5)
        tk.Label(
            success_dialog,
            text="平均偏差：2.5 cents",
            font=self.label_font,
            bg="#ffffff"
        ).pack(pady=5)
        button_frame = tk.Frame(success_dialog, bg="#ffffff")
        button_frame.pack(pady=20)
        def next_note_and_close():
            success_dialog.destroy()
            self.reset_practice()
            self.next_note(start=False)
            self.start_practice()
        tk.Button(
            button_frame,
            text="继续下一音",
            font=self.label_font,
            bg="#4caf50",
            fg="white",
            command=next_note_and_close
        ).pack(side=tk.LEFT, padx=10)
        def retry_and_close():
            success_dialog.destroy()
            self.reset_practice()
            self.start_practice()
        tk.Button(
            button_frame,
            text="再练一次",
            font=self.label_font,
            bg="#2196f3",
            fg="white",
            command=retry_and_close
        ).pack(side=tk.LEFT, padx=10)
    
    def update_pitch_chart(self):
        """更新音高偏差图表"""
        if len(self.cents_data) <= 1 or not hasattr(self, 'ax'):
            return
        
        self.ax.clear()
        
        # 设置Y轴范围（音分偏差）
        y_range = 50  # 上下各显示50音分
        self.ax.set_ylim(-y_range, y_range)
        
        # 获取音准容忍度阈值
        threshold = self.accuracy_tolerance.get()
        
        # 添加三色区域
        self.ax.axhspan(-threshold, threshold, color='#4caf50', alpha=0.3)  # 绿色区域（达标）
        self.ax.axhspan(threshold, y_range, color='#f44336', alpha=0.3)  # 红色区域（偏高）
        self.ax.axhspan(-y_range, -threshold, color='#2196f3', alpha=0.3)  # 蓝色区域（偏低）
        
        # 设置标签
        self.ax.set_ylabel('音分偏差 (cents)')
        self.ax.set_title(f'目标音符: {self.current_note.get()}')
        
        # 绘制偏差数据点
        self.ax.plot(range(len(self.cents_data)), self.cents_data, 'o-', markersize=3, color='#3f51b5')
        
        # 添加中心线和刻度
        self.ax.axhline(y=0, color='black', linestyle='-', linewidth=1)
        self.ax.axhline(y=threshold, color='black', linestyle='--', linewidth=0.5)
        self.ax.axhline(y=-threshold, color='black', linestyle='--', linewidth=0.5)
        
        # 设置X轴（改为帧数）
        self.ax.set_xlim(0, 100)
        self.ax.set_xlabel('帧数 (音频采样点)')
        
        # 重绘
        self.fig.tight_layout()
        self.canvas.draw()

    def update_instrument_settings(self, event=None):
        """根据选择的乐器更新设置"""
        instrument = self.instrument_type.get()
        if instrument == "钢琴" or instrument == "默认":
            # 为钢琴和默认设置相同的特定默认值
            self.volume_threshold_var.set(0.03)
            self.confidence_threshold_var.set(0.35)
        elif instrument == "大提琴": # 添加对大提琴的处理
            # 为大提琴设置特定的默认值
            self.volume_threshold_var.set(0.03)
            self.confidence_threshold_var.set(0.35)
        else:
            # 其他乐器使用通用默认值
            self.volume_threshold_var.set(0.05)
            self.confidence_threshold_var.set(0.70)
        
        # 更新标签显示
        self.update_volume_threshold_label(self.volume_threshold_var.get())
        self.update_confidence_threshold_label(self.confidence_threshold_var.get())

    # 新增方法：更新判断模式相关的UI
    def update_judgement_settings_ui(self):
        """根据选择的判断模式显示或隐藏时长设置UI"""
        # 确保所有相关组件都已创建
        if not hasattr(self, 'duration_setting_frame') or \
           not hasattr(self, 'frames_label') or \
           not hasattr(self.frames_label.master.winfo_children()[ self.frames_label.master.winfo_children().index(self.frames_label) -1 ], 'config'): # 检查滑动条是否存在
            return

        frames_scale_widget = self.frames_label.master.winfo_children()[ self.frames_label.master.winfo_children().index(self.frames_label) -1 ] # 帧数滑动条
        frames_title_label = self.frames_label.master.winfo_children()[ self.frames_label.master.winfo_children().index(frames_scale_widget) -1 ] # 帧数标题 "成功判定帧数:"


        if self.judgement_mode.get() == "duration":
            # 显示时长设置
            self.duration_setting_frame.pack(anchor="w", pady=(5,0), fill='x')
            # 禁用帧数相关的设置
            if hasattr(frames_title_label, 'config'): frames_title_label.config(fg="grey") # 变灰示意
            if hasattr(frames_scale_widget, 'config'): frames_scale_widget.config(state="disabled")
            if hasattr(self.frames_label, 'config'): self.frames_label.config(fg="grey")

        else: # "frames" 模式
            # 隐藏时长设置
            self.duration_setting_frame.pack_forget()
            # 启用帧数相关的设置
            if hasattr(frames_title_label, 'config'): frames_title_label.config(fg="black") # 恢复颜色
            if hasattr(frames_scale_widget, 'config'): frames_scale_widget.config(state="normal")
            if hasattr(self.frames_label, 'config'): self.frames_label.config(fg="black")
        
        self.update_target_duration_label() # 确保时长标签在显隐切换时也更新

    # 新增方法：更新目标时长标签
    def update_target_duration_label(self, value=None):
        """更新目标演奏时长的标签显示"""
        if hasattr(self, 'duration_label'): # 确保UI已创建
            self.duration_label.config(text=f"{self.target_duration.get():.1f} 秒")

    def change_note_group(self, notes):
        """更改音符组"""
        if notes and len(notes) > 0:
            # 更新当前音符为第一个音符
            self.current_note.set(notes[0])
            # 调用更新目标音符的方法
            self.update_target_note()


class MultiNotePracticeApp:
    """多音音准练习应用"""
    
    def __init__(self, root, main_app):
        self.root = root
        self.main_app = main_app
        
        # 设置字体
        self.title_font = ("Microsoft YaHei", 18, "bold")
        self.subtitle_font = ("Microsoft YaHei", 14)
        self.button_font = ("Microsoft YaHei", 12)
        self.label_font = ("Microsoft YaHei", 12)
        
        # 初始化变量
        self.current_notes = ["C4", "E4", "G4"]  # 默认C大三和弦
        self.completed_notes = []  # 已完成的音符
        self.practice_running = False
        self.current_target_index = 0  # 当前目标音符的索引
        self.accuracy_tolerance = 15  # 音准容忍度（cents）
        self.success_frames_needed = 3  # 达标所需连续帧数
        
        # 音量与置信度阈值 - 默认为钢琴设置
        self.volume_threshold_var = tk.DoubleVar(value=0.03)  # 音量敏感度
        self.confidence_threshold_var = tk.DoubleVar(value=0.35)  # 音高识别准确度
        
        # 图表数据初始化
        self.time_data = []  # 时间戳数据
        self.cents_data = []  # 音分偏差数据
        self.amplitude_data = []  # 音量数据
        
        # 音高检测相关变量
        self.consecutive_success_frames = 0  # 连续成功帧数
        self.audio_queue = queue.Queue()  # 音频数据队列
        
        # 如果PitchDetector可用，则初始化
        self.pitch_detector = None
        if PitchDetector is not None:
            try:
                self.pitch_detector = PitchDetector()
            except Exception as e:
                logger.error(f"初始化音高检测器失败: {e}")
        
        # 创建界面
        self.create_widgets()
        
        # 启动定时器处理音频数据
        self.root.after(10, self.process_audio_queue)
    
    def create_widgets(self):
        """创建多音练习界面组件"""
        # 导航栏
        nav_frame = tk.Frame(self.root, bg="#3f51b5", height=50)
        nav_frame.pack(fill="x")
        
        # 返回按钮
        back_button = tk.Button(
            nav_frame,
            text="返回",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=lambda: self.main_app.close_practice_window(self.root)
        )
        back_button.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 标题
        title_label = tk.Label(
            nav_frame,
            text="多音音准练习",
            font=self.title_font,
            bg="#3f51b5",
            fg="white"
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 设置按钮
        settings_button = tk.Button(
            nav_frame,
            text="设置",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=self.open_settings
        )
        settings_button.pack(side=tk.RIGHT, padx=20, pady=10)
        
        # 主体内容区
        content_frame = tk.Frame(self.root, bg="#f5f5f5")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 创建左右分区
        main_content_frame = tk.Frame(content_frame, bg="#f5f5f5")
        main_content_frame.pack(fill="both", expand=True)
        
        # 左侧设置面板
        left_panel = tk.Frame(main_content_frame, bg="#f5f5f5", width=200)
        left_panel.pack(side=tk.LEFT, fill="y", padx=(0, 10))
        self.create_settings_panel(left_panel)
        
        # 右侧练习区域
        right_panel = tk.Frame(main_content_frame, bg="#f5f5f5")
        right_panel.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 五线谱目标音符区
        self.create_staff_area(right_panel)
        
        # 钢琴键盘对应区
        self.create_keyboard_area(right_panel)
        
        # 音高检测反馈区
        self.create_feedback_area(right_panel)
        
        # 底部控制区
        bottom_frame = tk.Frame(self.root, bg="#f5f5f5")
        bottom_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.create_control_panel(bottom_frame)
    
    def create_settings_panel(self, parent):
        settings_frame = tk.Frame(parent, bg="#f5f5f5", width=300)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        settings_frame.pack_propagate(False)
        
        # 添加"练习设置"标题
        settings_title = tk.Label(settings_frame, text="练习设置", font=("Microsoft YaHei", 14, "bold"), bg="#f5f5f5")
        settings_title.pack(pady=(5, 15))
        
        # 单音选择（放在乐器类型前面）
        note_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        note_frame.pack(fill=tk.X, pady=5)
        note_label = tk.Label(note_frame, text="选择练习音符:", font=self.label_font, bg="#f5f5f5")
        note_label.pack(anchor=tk.W)
        note_options = ["C4", "D4", "E4", "F4", "G4", "A4", "B4", "C5"]
        note_var = tk.StringVar(value="C4")
        note_dropdown = ttk.Combobox(note_frame, textvariable=note_var, values=note_options, font=self.label_font, state="readonly", width=15)
        note_dropdown.pack(fill=tk.X, pady=5)
        note_dropdown.bind("<<ComboboxSelected>>", lambda e: self.current_note.set(note_var.get()))
        self.note_var = note_var
        
        # 乐器类型（放在音符选择后面）
        instrument_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        instrument_frame.pack(fill=tk.X, pady=5)
        instrument_label = tk.Label(instrument_frame, text="乐器类型:", font=self.label_font, bg="#f5f5f5")
        instrument_label.pack(anchor=tk.W)
        instrument_var = tk.StringVar(value="钢琴")
        instrument_dropdown = ttk.Combobox(instrument_frame, textvariable=instrument_var, 
                                           values=["钢琴", "小提琴", "大提琴", "长笛", "单簧管"], 
                                           font=self.label_font, state="readonly", width=15)
        instrument_dropdown.pack(fill=tk.X, pady=5)
        instrument_dropdown.bind("<<ComboboxSelected>>", lambda e: self.update_instrument_settings(instrument_var.get()))
        self.instrument_var = instrument_var

        # 音准要求
        accuracy_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        accuracy_frame.pack(fill=tk.X, pady=5)
        accuracy_label = tk.Label(accuracy_frame, text="音准要求 (±cents):", font=self.label_font, bg="#f5f5f5")
        accuracy_label.pack(anchor=tk.W)
        self.accuracy_tolerance_var = tk.IntVar(value=self.accuracy_tolerance)
        accuracy_scale = ttk.Scale(
            accuracy_frame,
            from_=5,
            to=30,
            orient="horizontal",
            variable=self.accuracy_tolerance_var,
            length=150
        )
        accuracy_scale.pack(anchor="w")
        self.accuracy_label = tk.Label(
            accuracy_frame,
            text=f"±{self.accuracy_tolerance} cents",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.accuracy_label.pack(anchor="w", pady=(0, 10))
        accuracy_scale.config(command=self.update_accuracy_label)

        # 成功判定帧数
        frames_frame = tk.Frame(settings_frame, bg="#f5f5f5")
        frames_frame.pack(fill=tk.X, pady=5)
        frames_label = tk.Label(frames_frame, text="成功判定帧数:", font=self.label_font, bg="#f5f5f5")
        frames_label.pack(anchor=tk.W)
        self.success_frames_var = tk.IntVar(value=self.success_frames_needed)
        frames_scale = ttk.Scale(
            frames_frame,
            from_=1,
            to=10,
            orient="horizontal",
            variable=self.success_frames_var,
            length=150
        )
        frames_scale.pack(fill=tk.X, pady=5)
        self.frames_label = tk.Label(
            frames_frame,
            text=f"连续 {self.success_frames_needed} 帧",
            font=self.label_font,
            bg="#f5f5f5"
        )
        self.frames_label.pack(anchor=tk.W)
        frames_scale.config(command=self.update_frames_label)

        # 练习模式
        tk.Label(
            settings_frame,
            text="练习模式:",
            font=self.label_font,
            bg="#f5f5f5"
        ).pack(anchor="w", pady=(5, 0))
        self.practice_mode = tk.StringVar(value="顺序模式")
        mode_options = ["顺序模式", "随机模式"]
        mode_dropdown = ttk.Combobox(
            settings_frame,
            textvariable=self.practice_mode,
            values=mode_options,
            font=self.label_font,
            width=10
        )
        mode_dropdown.pack(anchor="w", pady=(0, 10))
    
    def create_staff_area(self, parent):
        """创建五线谱目标音符区"""
        staff_frame = tk.LabelFrame(
            parent,
            text="五线谱目标音符区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        staff_frame.pack(fill="x", pady=(0, 10))
        
        # 音符显示区
        notes_frame = tk.Frame(staff_frame, bg="#ffffff")
        notes_frame.pack(pady=10)
        
        # 显示多个音符（简化表示）
        for i, note in enumerate(self.current_notes):
            # 判断音符是否已完成
            if note in self.completed_notes:
                bg_color = "#4caf50"  # 绿色（已完成）
                fg_color = "#ffffff"  # 白色文字
            elif i == self.current_target_index and self.practice_running:
                bg_color = "#2196f3"  # 蓝色（当前目标）
                fg_color = "#ffffff"  # 白色文字
            else:
                bg_color = "#f5f5f5"  # 灰色（未完成）
                fg_color = "#333333"  # 深灰色文字
            
            note_frame = tk.Frame(notes_frame, bg="#ffffff", padx=5)
            note_frame.pack(side=tk.LEFT, padx=10)
            
            # 音符背景框
            note_bg = tk.Frame(note_frame, bg=bg_color, width=60, height=60)
            note_bg.pack()
            note_bg.pack_propagate(False)  # 固定大小
            
            # 音符名称
            tk.Label(
                note_bg,
                text=note,
                font=("Microsoft YaHei", 24, "bold"),
                bg=bg_color,
                fg=fg_color
            ).pack(expand=True)
            
            # 音符状态指示
            status_text = "已完成" if note in self.completed_notes else "待练习"
            if i == self.current_target_index and self.practice_running and note not in self.completed_notes:
                status_text = "当前目标"
                
            tk.Label(
                note_frame,
                text=status_text,
                font=("Microsoft YaHei", 10),
                bg="#ffffff",
                fg=bg_color
            ).pack(pady=(5, 0))
    
    def create_keyboard_area(self, parent):
        """创建钢琴键盘显示区"""
        keyboard_frame = tk.LabelFrame(
            parent,
            text="钢琴键盘对应区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        keyboard_frame.pack(fill="x", pady=(0, 10))
        
        keyboard_canvas = tk.Canvas(keyboard_frame, height=80, bg="white")
        keyboard_canvas.pack(fill="x", pady=10)
        
        # 绘制钢琴键盘（简化版本）
        key_width = 40
        white_keys = ["C", "D", "E", "F", "G", "A", "B"]
        black_keys = [0, 1, 3, 4, 5]  # 黑键位置（相对于白键的索引）
        
        # 绘制白键
        for i, key in enumerate(white_keys):
            x1 = 50 + i * key_width
            y1 = 10
            x2 = x1 + key_width
            y2 = 70
            
            # 当前目标音高亮显示
            fill_color = "#ffffff"
            outline_color = "black"
            
            # 检查是否为当前目标音符
            current_target = self.current_notes[self.current_target_index] if self.current_target_index < len(self.current_notes) else None
            
            for note in self.current_notes:
                if note.startswith(key):
                    # 检查是否为当前目标
                    if note == current_target and note not in self.completed_notes and self.practice_running:
                        fill_color = "#2196f3"  # 蓝色（当前目标）
                        outline_color = "blue"
                    # 检查是否已完成
                    elif note in self.completed_notes:
                        fill_color = "#4caf50"  # 绿色（已完成）
                    else:
                        fill_color = "#bbdefb"  # 浅蓝色（未完成）
            
            keyboard_canvas.create_rectangle(x1, y1, x2, y2, fill=fill_color, outline=outline_color)
            keyboard_canvas.create_text(x1 + key_width/2, y2 - 15, text=f"{key}4")
        
        # 绘制黑键
        for i in black_keys:
            if i < len(white_keys) - 1:  # 确保不会超出白键范围
                x1 = 50 + i * key_width + key_width * 0.7
                y1 = 10
                x2 = x1 + key_width * 0.6
                y2 = 45
                
                # 默认黑色
                fill_color = "black"
                
                # 检查是否为目标音符（简化处理，实际应考虑升降号）
                for note in self.current_notes:
                    if note.startswith(f"{white_keys[i]}#") or note.startswith(f"{white_keys[i+1]}b"):
                        # 检查是否为当前目标
                        if note == current_target and note not in self.completed_notes and self.practice_running:
                            fill_color = "#2196f3"  # 蓝色（当前目标）
                        # 检查是否已完成
                        elif note in self.completed_notes:
                            fill_color = "#4caf50"  # 绿色（已完成）
                        else:
                            fill_color = "#bbdefb"  # 浅蓝色（未完成）
                
                keyboard_canvas.create_rectangle(x1, y1, x2, y2, fill=fill_color)
        
        # 保存画布引用以便更新
        self.keyboard_canvas = keyboard_canvas
    
    def create_feedback_area(self, parent):
        """创建音高检测反馈区"""
        feedback_frame = tk.LabelFrame(
            parent,
            text="音高检测反馈区",
            font=self.subtitle_font,
            bg="#ffffff",
            padx=10,
            pady=10
        )
        feedback_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # 简化的反馈区（与单音练习类似）
        # 创建一个简单的音高指示器
        indicator_frame = tk.Frame(feedback_frame, bg="#ffffff")
        indicator_frame.pack(fill="x", pady=10)
        
        # 标签
        tk.Label(
            indicator_frame,
            text="偏高 |",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(50, 0))
        
        # 简单的指示器
        indicator_canvas = tk.Canvas(indicator_frame, width=200, height=30, bg="#ffffff", highlightthickness=0)
        indicator_canvas.pack(side=tk.LEFT)
        
        # 画指示器中心线
        indicator_canvas.create_line(0, 15, 200, 15, fill="#cccccc")
        
        # 画偏差标记
        indicator_canvas.create_line(100, 5, 100, 25, fill="#000000", width=2)
        
        # 画箭头指示当前音高（居中表示匹配）
        indicator_canvas.create_polygon(100, 5, 110, 15, 100, 25, 90, 15, fill="#4caf50")
        
        tk.Label(
            indicator_frame,
            text="| 偏低",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT)
        
        # 当前检测信息
        info_frame = tk.Frame(feedback_frame, bg="#ffffff")
        info_frame.pack(fill="x", pady=10)
        
        tk.Label(
            info_frame,
            text="检测到音符：",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.detected_note_label = tk.Label(
            info_frame,
            text="E4",
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff"
        )
        self.detected_note_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            info_frame,
            text="偏差：",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.deviation_label = tk.Label(
            info_frame,
            text="+2 cents",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.deviation_label.pack(side=tk.LEFT)
        
        # 进度信息
        progress_frame = tk.Frame(feedback_frame, bg="#ffffff")
        progress_frame.pack(fill="x", pady=10)
        
        tk.Label(
            progress_frame,
            text="已完成：",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.completed_label = tk.Label(
            progress_frame,
            text="1/3音",
            font=("Microsoft YaHei", 14, "bold"),
            bg="#ffffff"
        )
        self.completed_label.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(
            progress_frame,
            text="剩余：",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(20, 5))
        
        self.remaining_label = tk.Label(
            progress_frame,
            text="C4, G4",
            font=("Microsoft YaHei", 14),
            bg="#ffffff"
        )
        self.remaining_label.pack(side=tk.LEFT)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 控制按钮
        self.start_button = tk.Button(
            parent,
            text="开始练习",
            font=self.button_font,
            bg="#4caf50",
            fg="white",
            padx=15,
            pady=8,
            command=self.start_practice
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.pause_button = tk.Button(
            parent,
            text="暂停",
            font=self.button_font,
            bg="#ff9800",
            fg="white",
            padx=15,
            pady=8,
            state="disabled",
            command=self.pause_practice
        )
        self.pause_button.pack(side=tk.LEFT, padx=5)
        
        self.reset_button = tk.Button(
            parent,
            text="重置",
            font=self.button_font,
            bg="#f44336",
            fg="white",
            padx=15,
            pady=8,
            state="disabled",
            command=self.reset_practice
        )
        self.reset_button.pack(side=tk.LEFT, padx=5)
        
        self.next_button = tk.Button(
            parent,
            text="下一组音符",
            font=self.button_font,
            bg="#2196f3",
            fg="white",
            padx=15,
            pady=8,
            command=self.next_note_group
        )
        self.next_button.pack(side=tk.LEFT, padx=5)
    
    def process_audio_queue(self):
        """处理音频数据队列"""
        try:
            while not self.audio_queue.empty():
                data = self.audio_queue.get_nowait()
                if data is not None:
                    frequency, note_name, confidence, cents_deviation, amplitude = data
                    
                    # 如果检测到有效音高并且置信度足够
                    if frequency is not None and frequency > 0:
                        # 更新UI（只传递必要的参数）
                        self.update_pitch_ui(frequency, note_name, confidence, cents_deviation)
        except Exception as e:
            logger.error(f"处理音频数据出错: {e}")
        finally:
            # 继续定时器
            self.root.after(10, self.process_audio_queue)
    
    def pitch_callback(self, frequency, note_name, confidence, cents_deviation, amplitude):
        """音高检测回调函数"""
        if not self.practice_running:
            return
        
        # 将数据放入队列
        self.audio_queue.put((frequency, note_name, confidence, cents_deviation, amplitude))
    
    def update_pitch_ui(self, frequency, note_name, confidence, cents_deviation):
        """更新音高相关UI"""
        # 更新检测到的音符显示
        self.detected_note_label.config(
            text=note_name,
            fg="#3f51b5"
        )
        
        # 更新偏差显示
        self.deviation_label.config(
            text=f"{cents_deviation:+.1f} cents",
            fg="#3f51b5" if abs(cents_deviation) <= self.accuracy_tolerance else "#f44336"
        )
        
        # 获取当前目标音符
        current_target = self.current_notes[self.current_target_index]
        
        # 检查当前音符是否匹配目标音符
        if note_name == current_target:
            # 音符匹配且音准在容忍度范围内
            if abs(cents_deviation) <= self.accuracy_tolerance:
                self.consecutive_success_frames += 1
                
                # 如果连续成功帧数达到要求
                if self.consecutive_success_frames >= self.success_frames_needed:
                    # 标记音符为已完成
                    if current_target not in self.completed_notes:
                        self.completed_notes.append(current_target)
                        
                        # 选择下一个未完成的音符作为目标
                        self.select_next_target()
                        
                        # 更新显示
                        self.update_display()
                        
                        # 检查是否所有音符都已完成
                        if len(self.completed_notes) == len(self.current_notes):
                            messagebox.showinfo("恭喜", "所有音符都已完成！")
                            self.reset_practice()
                    
                    # 重置连续成功帧数
                    self.consecutive_success_frames = 0
        else:
            # 音符不匹配，重置连续成功帧数
            self.consecutive_success_frames = 0
            
        # 更新进度信息
        remaining_notes = [note for note in self.current_notes if note not in self.completed_notes]
        self.completed_label.config(text=f"{len(self.completed_notes)}/{len(self.current_notes)}音")
        self.remaining_label.config(text=", ".join(remaining_notes))

        # 生成当前时间戳
        timestamp = time.time()

        # 更新图表数据 (通用部分)
        self.time_data.append(timestamp) # 使用实际时间戳
        self.cents_data.append(cents_deviation)
        
        # 保持数据长度在100以内 (图表显示数据)
        if len(self.time_data) > 100:
            self.time_data = self.time_data[-100:]
            self.cents_data = self.cents_data[-100:]
            if len(self.amplitude_data) > 100:
                self.amplitude_data = self.amplitude_data[-100:]
        
        self.update_pitch_chart() # 最后更新图表
    
    def select_next_target(self):
        """选择下一个未完成的音符作为目标"""
        # 找出所有未完成的音符
        remaining_notes = [i for i, note in enumerate(self.current_notes) 
                           if note not in self.completed_notes]
        
        # 如果还有未完成的音符，设置为下一个目标
        if remaining_notes:
            self.current_target_index = remaining_notes[0]
        else:
            # 所有音符都已完成，保持当前索引
            pass
    
    def start_practice(self):
        """开始多音练习"""
        # 检查音高检测器是否可用
        if self.pitch_detector is None:
            messagebox.showerror("错误", "音高检测器不可用，无法开始练习")
            return
        
        # 更新按钮状态
        self.start_button.config(state="disabled")
        self.pause_button.config(state="normal")
        self.reset_button.config(state="normal")
        
        # 设置练习状态
        self.practice_running = True
        
        # 重置统计和状态
        self.completed_notes = []
        self.consecutive_success_frames = 0
        self.current_target_index = 0  # 从第一个音符开始
        
        # 设置目标音符
        current_target = self.current_notes[self.current_target_index]
        self.pitch_detector.set_target_note(current_target)
        
        # 启动音高检测
        try:
            self.pitch_detector.start(callback=self.pitch_callback)
            logger.info(f"开始多音练习: 目标音符组={', '.join(self.current_notes)}")
        except Exception as e:
            messagebox.showerror("错误", f"启动音高检测失败: {e}")
            self.reset_practice()
            logger.error(f"启动音高检测失败: {e}")
        
        # 更新显示
        self.update_display()
    
    def pause_practice(self):
        """暂停多音练习"""
        # 更新按钮状态
        self.start_button.config(state="normal")
        self.pause_button.config(state="disabled")
        
        # 暂停音高检测
        if self.pitch_detector and self.practice_running:
            self.pitch_detector.stop()
        
        # 设置练习状态
        self.practice_running = False
        
        # 不重置状态文本，保留当前状态信息
        logger.info("暂停多音练习，保留当前状态显示")
    
    def reset_practice(self):
        """重置多音练习"""
        # 更新按钮状态
        self.start_button.config(state="normal")
        self.pause_button.config(state="disabled")
        self.reset_button.config(state="disabled")
        
        # 停止音高检测
        if self.pitch_detector and self.practice_running:
            self.pitch_detector.stop()
        
        # 设置练习状态
        self.practice_running = False
        
        # 清空已完成音符列表
        self.completed_notes = []
        self.consecutive_success_frames = 0
        
        # 更新显示
        self.update_display()
    
    def next_note_group(self):
        """切换到下一组音符"""
        # 预设一些音符组合
        note_groups = [
            ["C4", "E4", "G4"],  # C大三和弦
            ["F4", "A4", "C5"],  # F大三和弦
            ["G4", "B4", "D5"],  # G大三和弦
            ["C4", "D4", "E4", "F4", "G4"]  # C大调前五个音
        ]
        
        # 查找当前组合的索引
        current_group_str = ",".join(sorted(self.current_notes))
        next_group = note_groups[0]  # 默认切换到第一组
        
        for i, group in enumerate(note_groups):
            if current_group_str == ",".join(sorted(group)):
                next_group = note_groups[(i + 1) % len(note_groups)]
                break
        
        # 更新当前音符组
        self.current_notes = next_group
        self.completed_notes = []
        
        # 更新显示
        self.update_display()
    
    def update_display(self):
        """更新界面显示"""
        # 更新进度信息
        remaining_notes = [note for note in self.current_notes if note not in self.completed_notes]
        
        # 查找主要内容区域
        content_frames = [widget for widget in self.root.winfo_children() 
                        if isinstance(widget, tk.Frame) and widget not in [self.root.winfo_children()[0]]]
        
        if content_frames:
            # 找到主内容区域
            main_content_frame = content_frames[0]
            
            # 保留主内容区域，但移除右侧区域
            for widget in main_content_frame.winfo_children():
                if widget != main_content_frame.winfo_children()[0]:  # 保留左侧设置面板
                    widget.destroy()
            
            # 创建新的右侧区域
            right_panel = tk.Frame(main_content_frame, bg="#f5f5f5")
            right_panel.pack(side=tk.LEFT, fill="both", expand=True)
            
            # 更新右侧内容
            self.create_staff_area(right_panel)
            self.create_keyboard_area(right_panel)
            self.create_feedback_area(right_panel)
            
            # 删除底部控制区并重新创建
            bottom_frames = [widget for widget in self.root.winfo_children() 
                           if isinstance(widget, tk.Frame) and widget != main_content_frame and widget != self.root.winfo_children()[0]]
            
            if bottom_frames:
                for frame in bottom_frames:
                    frame.destroy()
            
            # 重新创建底部控制区
            bottom_frame = tk.Frame(self.root, bg="#f5f5f5")
            bottom_frame.pack(fill="x", padx=20, pady=(0, 20))
            
            self.create_control_panel(bottom_frame)
        else:
            # 如果找不到现有框架，就重建整个界面
            # 清除除导航栏外的所有组件
            for widget in self.root.winfo_children():
                        if widget != self.root.winfo_children()[0]:  # 保留导航栏
                            widget.destroy()
        
        # 重新创建内容区
        content_frame = tk.Frame(self.root, bg="#f5f5f5")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 创建左右分区
        main_content_frame = tk.Frame(content_frame, bg="#f5f5f5")
        main_content_frame.pack(fill="both", expand=True)
        
        # 左侧设置面板
        left_panel = tk.Frame(main_content_frame, bg="#f5f5f5", width=200)
        left_panel.pack(side=tk.LEFT, fill="y", padx=(0, 10))
        self.create_settings_panel(left_panel)
        
        # 右侧练习区域
        right_panel = tk.Frame(main_content_frame, bg="#f5f5f5")
        right_panel.pack(side=tk.LEFT, fill="both", expand=True)
        
        self.create_staff_area(right_panel)
        self.create_keyboard_area(right_panel)
        self.create_feedback_area(right_panel)
            
            # 底部控制区
        bottom_frame = tk.Frame(self.root, bg="#f5f5f5")
        bottom_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.create_control_panel(bottom_frame)
        
        # 如果正在练习，设置当前目标音符
        if self.practice_running and self.pitch_detector and len(self.current_notes) > self.current_target_index:
            target_note = self.current_notes[self.current_target_index]
            self.pitch_detector.set_target_note(target_note)
            
            # 添加当前目标音符指示
            if hasattr(self, 'detected_note_label'):
                # 显示当前目标音符
                tk.Label(
                    self.detected_note_label.master,
                    text="当前目标：",
                    font=self.label_font,
                    bg="#ffffff"
                ).pack(side=tk.LEFT, padx=(50, 5))
                
                target_label = tk.Label(
                    self.detected_note_label.master,
                    text=target_note,
                    font=("Microsoft YaHei", 14, "bold"),
                    bg="#ffffff",
                    fg="#4caf50"
                )
                target_label.pack(side=tk.LEFT)
    
    def open_settings(self):
        """打开设置对话框"""
        messagebox.showinfo("功能开发中", "设置功能正在开发中，敬请期待！")

    # 新增方法用于更新音准标签
    def update_accuracy_label(self, value=None):
        """更新音准要求标签"""
        # 从变量获取值
        tolerance = self.accuracy_tolerance_var.get()
        # 更新实例变量
        self.accuracy_tolerance = tolerance
        # 更新标签
        self.accuracy_label.config(text=f"±{tolerance} cents")
    
    # 新增方法用于更新连续帧数标签
    def update_frames_label(self, value=None):
        """更新成功判定帧数标签"""
        # 从变量获取值
        frames = self.success_frames_var.get()
        # 更新实例变量
        self.success_frames_needed = frames
        # 更新标签
        self.frames_label.config(text=f"连续 {frames} 帧")
    
    # 新增方法用于切换音符组合
    def change_note_group(self, notes):
        """切换音符组合"""
        self.current_notes = notes
        self.completed_notes = []
        self.current_target_index = 0
        # 更新显示
        self.update_display()

    def update_target_note(self, event=None):
        """更新目标音符组"""
        # 获取音符组名称并更新音符组
        note_groups = {
            "C大三和弦": ["C4", "E4", "G4"],
            "F大三和弦": ["F4", "A4", "C5"],
            "G大三和弦": ["G4", "B4", "D5"],
            "C大调音阶": ["C4", "D4", "E4", "F4", "G4"]
        }
        selected_group = self.note_group_var.get()
        if selected_group in note_groups:
            self.change_note_group(note_groups[selected_group])
    
    def update_instrument_settings(self, instrument_type):
        """根据乐器类型更新音量和音高识别阈值"""
        # 根据乐器类型设置音量和置信度阈值
        if instrument_type in ["钢琴", "默认", "大提琴"]:
            # 钢琴、默认和大提琴使用较低的阈值
            self.volume_threshold_var.set(0.03)
            self.confidence_threshold_var.set(0.35)
        else:
            # 其他乐器使用较高的阈值
            self.volume_threshold_var.set(0.05)
            self.confidence_threshold_var.set(0.70)
        
        # 更新UI显示（如果需要）
        print(f"已更新乐器类型为 {instrument_type}，音量敏感度：{self.volume_threshold_var.get():.2f}，音高识别准确度：{self.confidence_threshold_var.get():.2f}")


class SettingsApp:
    """设置应用"""
    
    def __init__(self, root, main_app):
        self.root = root
        self.main_app = main_app
        
        # 设置字体
        self.title_font = ("Microsoft YaHei", 18, "bold")
        self.subtitle_font = ("Microsoft YaHei", 14)
        self.button_font = ("Microsoft YaHei", 12)
        self.label_font = ("Microsoft YaHei", 12)
        
        # 创建设置界面
        self.create_widgets()
    
    def create_widgets(self):
        """创建设置界面组件"""
        # 导航栏
        nav_frame = tk.Frame(self.root, bg="#3f51b5", height=50)
        nav_frame.pack(fill="x")
        
        # 返回按钮
        back_button = tk.Button(
            nav_frame,
            text="返回",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=lambda: self.main_app.close_practice_window(self.root)
        )
        back_button.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 标题
        title_label = tk.Label(
            nav_frame,
            text="设置",
            font=self.title_font,
            bg="#3f51b5",
            fg="white"
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 主体内容区
        content_frame = tk.Frame(self.root, bg="#f5f5f5")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 练习设置区域
        self.create_practice_settings(content_frame)
        
        # 音符范围设置区域
        self.create_note_range_settings(content_frame)
        
        # 界面设置区域
        self.create_interface_settings(content_frame)
        
        # 底部保存按钮
        save_button = tk.Button(
            self.root,
            text="保存设置",
            font=self.button_font,
            bg="#4caf50",
            fg="white",
            padx=20,
            pady=10,
            command=self.save_settings
        )
        save_button.pack(pady=20)
    
    def create_practice_settings(self, parent):
        """创建练习设置区域"""
        practice_frame = tk.LabelFrame(
            parent,
            text="练习设置",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        practice_frame.pack(fill="x", pady=(0, 15))
        
        # 音准容忍度设置
        tk.Label(
            practice_frame,
            text="音准容忍度：",
            font=self.label_font,
            bg="#f5f5f5",
            anchor="w"
        ).grid(row=0, column=0, sticky="w", pady=5)
        
        # 创建单选按钮框架
        tolerance_frame = tk.Frame(practice_frame, bg="#f5f5f5")
        tolerance_frame.grid(row=0, column=1, sticky="w", pady=5)
        
        # 音准容忍度单选按钮
        self.tolerance_var = tk.StringVar(value="中级")
        
        tk.Radiobutton(
            tolerance_frame,
            text="初级 (±15 cents)",
            variable=self.tolerance_var,
            value="初级",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=0, sticky="w")
        
        tk.Radiobutton(
            tolerance_frame,
            text="中级 (±10 cents)",
            variable=self.tolerance_var,
            value="中级",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=1, column=0, sticky="w")
        
        tk.Radiobutton(
            tolerance_frame,
            text="高级 (±5 cents)",
            variable=self.tolerance_var,
            value="高级",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=2, column=0, sticky="w")
        
        # 达标帧数设置
        tk.Label(
            practice_frame,
            text="达标帧数：",
            font=self.label_font,
            bg="#f5f5f5",
            anchor="w"
        ).grid(row=1, column=0, sticky="w", pady=15)
        
        # 创建达标帧数框架
        frames_frame = tk.Frame(practice_frame, bg="#f5f5f5")
        frames_frame.grid(row=1, column=1, sticky="w", pady=15)
        
        # 达标帧数单选按钮
        self.frames_var = tk.StringVar(value="标准")
        
        tk.Radiobutton(
            frames_frame,
            text="简单 (连续3帧)",
            variable=self.frames_var,
            value="简单",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=0, sticky="w")
        
        tk.Radiobutton(
            frames_frame,
            text="标准 (连续5帧)",
            variable=self.frames_var,
            value="标准",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=1, column=0, sticky="w")
        
        tk.Radiobutton(
            frames_frame,
            text="挑战 (连续8帧)",
            variable=self.frames_var,
            value="挑战",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=2, column=0, sticky="w")
        
        # 练习模式设置
        tk.Label(
            practice_frame,
            text="练习模式：",
            font=self.label_font,
            bg="#f5f5f5",
            anchor="w"
        ).grid(row=2, column=0, sticky="w", pady=15)
        
        # 创建练习模式框架
        mode_frame = tk.Frame(practice_frame, bg="#f5f5f5")
        mode_frame.grid(row=2, column=1, sticky="w", pady=15)
        
        # 练习模式单选按钮
        self.mode_var = tk.StringVar(value="连续练习")
        
        tk.Radiobutton(
            mode_frame,
            text="单次练习",
            variable=self.mode_var,
            value="单次练习",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=0, sticky="w")
        
        tk.Radiobutton(
            mode_frame,
            text="连续练习",
            variable=self.mode_var,
            value="连续练习",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=1, column=0, sticky="w")
        
        tk.Radiobutton(
            mode_frame,
            text="闯关模式",
            variable=self.mode_var,
            value="闯关模式",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=2, column=0, sticky="w")
    
    def create_note_range_settings(self, parent):
        """创建音符范围设置区域"""
        range_frame = tk.LabelFrame(
            parent,
            text="音符范围",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        range_frame.pack(fill="x", pady=(0, 15))
        
        # 最低音符设置
        tk.Label(
            range_frame,
            text="最低音符：",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=0, padx=(0, 10), sticky="w")
        
        self.min_note_var = tk.StringVar(value="C4")
        note_options = ["C3", "D3", "E3", "F3", "G3", "A3", "B3", "C4", "D4", "E4", "F4", "G4"]
        
        min_note_dropdown = ttk.Combobox(
            range_frame,
            textvariable=self.min_note_var,
            values=note_options,
            font=self.label_font,
            width=5
        )
        min_note_dropdown.grid(row=0, column=1, padx=5, pady=10, sticky="w")
        
        # 最高音符设置
        tk.Label(
            range_frame,
            text="最高音符：",
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=2, padx=(50, 10), sticky="w")
        
        self.max_note_var = tk.StringVar(value="C6")
        max_note_options = ["C5", "D5", "E5", "F5", "G5", "A5", "B5", "C6", "D6", "E6", "F6", "G6"]
        
        max_note_dropdown = ttk.Combobox(
            range_frame,
            textvariable=self.max_note_var,
            values=max_note_options,
            font=self.label_font,
            width=5
        )
        max_note_dropdown.grid(row=0, column=3, padx=5, pady=10, sticky="w")
    
    def create_interface_settings(self, parent):
        """创建界面设置区域"""
        interface_frame = tk.LabelFrame(
            parent,
            text="界面设置",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        interface_frame.pack(fill="x")
        
        # 界面选项复选框
        self.show_keyboard_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            interface_frame,
            text="显示钢琴键盘",
            variable=self.show_keyboard_var,
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=0, column=0, sticky="w", pady=5)
        
        self.show_deviation_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            interface_frame,
            text="显示音高偏差值",
            variable=self.show_deviation_var,
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=1, column=0, sticky="w", pady=5)
        
        self.show_encouragement_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            interface_frame,
            text="成功后显示鼓励信息",
            variable=self.show_encouragement_var,
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=2, column=0, sticky="w", pady=5)
        
        self.auto_save_var = tk.BooleanVar(value=False)
        tk.Checkbutton(
            interface_frame,
            text="每次练习后自动保存记录",
            variable=self.auto_save_var,
            font=self.label_font,
            bg="#f5f5f5"
        ).grid(row=3, column=0, sticky="w", pady=5)
    
    def save_settings(self):
        """保存设置"""
        # 在实际应用中，这里会保存设置到配置文件或数据库
        messagebox.showinfo("保存成功", "设置已保存！")
        
        # 关闭设置窗口，返回主界面
        self.main_app.close_practice_window(self.root)


class HistoryApp:
    """历史记录应用"""
    
    def __init__(self, root, main_app):
        self.root = root
        self.main_app = main_app
        
        # 设置字体
        self.title_font = ("Microsoft YaHei", 18, "bold")
        self.subtitle_font = ("Microsoft YaHei", 14)
        self.button_font = ("Microsoft YaHei", 12)
        self.label_font = ("Microsoft YaHei", 12)
        
        # 模拟数据
        self.history_data = [
            {"date": "2023-6-10", "note": "C4", "success_rate": "90%", "deviation": "±3c"},
            {"date": "2023-6-10", "note": "D4", "success_rate": "85%", "deviation": "±5c"},
            {"date": "2023-6-9", "note": "C4-G4", "success_rate": "70%", "deviation": "±8c"},
            {"date": "2023-6-8", "note": "E4", "success_rate": "95%", "deviation": "±2c"},
            {"date": "2023-6-7", "note": "F4", "success_rate": "75%", "deviation": "±6c"},
            {"date": "2023-6-7", "note": "G4", "success_rate": "80%", "deviation": "±4c"},
            {"date": "2023-6-6", "note": "A4", "success_rate": "65%", "deviation": "±7c"},
            {"date": "2023-6-5", "note": "B4", "success_rate": "60%", "deviation": "±9c"}
        ]
        
        # 创建历史记录界面
        self.create_widgets()
    
    def create_widgets(self):
        """创建历史记录界面组件"""
        # 导航栏
        nav_frame = tk.Frame(self.root, bg="#3f51b5", height=50)
        nav_frame.pack(fill="x")
        
        # 返回按钮
        back_button = tk.Button(
            nav_frame,
            text="返回",
            font=self.button_font,
            bg="#3f51b5",
            fg="white",
            bd=0,
            command=lambda: self.main_app.close_practice_window(self.root)
        )
        back_button.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 标题
        title_label = tk.Label(
            nav_frame,
            text="历史记录",
            font=self.title_font,
            bg="#3f51b5",
            fg="white"
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 主体内容区
        content_frame = tk.Frame(self.root, bg="#f5f5f5")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 练习日历区域
        self.create_calendar_section(content_frame)
        
        # 最近练习记录区域
        self.create_recent_history_section(content_frame)
        
        # 进步统计区域
        self.create_progress_stats_section(content_frame)
        
        # 底部按钮区域
        button_frame = tk.Frame(self.root, bg="#f5f5f5")
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        export_button = tk.Button(
            button_frame,
            text="导出数据",
            font=self.button_font,
            bg="#2196f3",
            fg="white",
            padx=15,
            pady=8,
            command=self.export_data
        )
        export_button.pack(side=tk.LEFT, padx=10)
        
        clear_button = tk.Button(
            button_frame,
            text="清除数据",
            font=self.button_font,
            bg="#f44336",
            fg="white",
            padx=15,
            pady=8,
            command=self.clear_data
        )
        clear_button.pack(side=tk.LEFT, padx=10)
    
    def create_calendar_section(self, parent):
        """创建练习日历区域"""
        calendar_frame = tk.LabelFrame(
            parent,
            text="练习日历",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        calendar_frame.pack(fill="x", pady=(0, 15))
        
        # 创建一个简单的月历视图（模拟）
        month_frame = tk.Frame(calendar_frame, bg="#ffffff", padx=10, pady=10)
        month_frame.pack(fill="x")
        
        # 月份标题
        month_label = tk.Label(
            month_frame,
            text="2023年6月",
            font=self.subtitle_font,
            bg="#ffffff"
        )
        month_label.pack(pady=(0, 10))
        
        # 创建日历网格
        days_frame = tk.Frame(month_frame, bg="#ffffff")
        days_frame.pack()
        
        # 星期标题
        weekdays = ["一", "二", "三", "四", "五", "六", "日"]
        for i, day in enumerate(weekdays):
            tk.Label(
                days_frame,
                text=day,
                font=self.label_font,
                width=4,
                height=2,
                bg="#f5f5f5"
            ).grid(row=0, column=i, padx=2, pady=2)
        
        # 日期单元格
        # 假设6月1日是星期四(索引3)
        day = 1
        for week in range(5):  # 5周
            for weekday in range(7):  # 7天
                if week == 0 and weekday < 3:  # 6月1日前的空白
                    cell = tk.Label(days_frame, width=4, height=2, bg="#ffffff")
                elif day <= 30:  # 6月有30天
                    # 确定颜色（模拟练习强度）
                    if day in [5, 6, 7, 8, 9, 10]:  # 多次练习
                        bg_color = "#4caf50"  # 绿色（练习多）
                    elif day in [15, 16, 20, 25]:  # 少次练习
                        bg_color = "#8bc34a"  # 浅绿色（练习少）
                    else:
                        bg_color = "#ffffff"  # 白色（无练习）
                    
                    cell = tk.Label(
                        days_frame,
                        text=str(day),
                        width=4,
                        height=2,
                        bg=bg_color,
                        fg="black" if bg_color == "#ffffff" else "white"
                    )
                    day += 1
                else:
                    cell = tk.Label(days_frame, width=4, height=2, bg="#ffffff")
                
                cell.grid(row=week+1, column=weekday, padx=2, pady=2)
        
        # 颜色图例
        legend_frame = tk.Frame(month_frame, bg="#ffffff", pady=10)
        legend_frame.pack(fill="x")
        
        tk.Label(
            legend_frame,
            text="颜色图例：",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Label(
            legend_frame,
            text="无练习",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        tk.Label(
            legend_frame,
            width=2,
            height=1,
            bg="#ffffff",
            relief="solid",
            borderwidth=1
        ).pack(side=tk.LEFT, padx=(0, 15))
        
        tk.Label(
            legend_frame,
            text="少量练习",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        tk.Label(
            legend_frame,
            width=2,
            height=1,
            bg="#8bc34a"
        ).pack(side=tk.LEFT, padx=(0, 15))
        
        tk.Label(
            legend_frame,
            text="大量练习",
            font=self.label_font,
            bg="#ffffff"
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        tk.Label(
            legend_frame,
            width=2,
            height=1,
            bg="#4caf50"
        ).pack(side=tk.LEFT)
    
    def create_recent_history_section(self, parent):
        """创建最近练习记录区域"""
        history_frame = tk.LabelFrame(
            parent,
            text="最近练习记录",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        history_frame.pack(fill="x", pady=(0, 15))
        
        # 创建表格
        columns = ("date", "note", "success_rate", "deviation")
        self.history_tree = ttk.Treeview(
            history_frame,
            columns=columns,
            show="headings",
            height=6
        )
        
        # 定义列
        self.history_tree.heading("date", text="日期")
        self.history_tree.heading("note", text="音符")
        self.history_tree.heading("success_rate", text="成功率")
        self.history_tree.heading("deviation", text="偏差")
        
        self.history_tree.column("date", width=100)
        self.history_tree.column("note", width=100)
        self.history_tree.column("success_rate", width=100)
        self.history_tree.column("deviation", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.history_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 添加数据
        for i, item in enumerate(self.history_data):
            self.history_tree.insert(
                "",
                "end",
                values=(item["date"], item["note"], item["success_rate"], item["deviation"])
            )
    
    def create_progress_stats_section(self, parent):
        """创建进步统计区域"""
        stats_frame = tk.LabelFrame(
            parent,
            text="进步统计",
            font=self.subtitle_font,
            bg="#f5f5f5",
            padx=15,
            pady=15
        )
        stats_frame.pack(fill="x")
        
        # 音符掌握度图表
        mastery_frame = tk.Frame(stats_frame, bg="#ffffff", padx=10, pady=10)
        mastery_frame.pack(fill="x", pady=(0, 15))
        
        tk.Label(
            mastery_frame,
            text="音符掌握度图表",
            font=self.label_font,
            bg="#ffffff"
        ).pack(pady=(0, 10))
        
        # 创建简单的条形图
        notes = ["C4", "D4", "E4", "F4", "G4", "A4", "B4", "C5"]
        mastery = [90, 85, 78, 65, 72, 60, 58, 50]  # 百分比
        
        chart_frame = tk.Frame(mastery_frame, bg="#ffffff")
        chart_frame.pack(fill="x")
        
        max_width = 300  # 最大条形宽度
        
        for i, note in enumerate(notes):
            # 音符标签
            tk.Label(
                chart_frame,
                text=note,
                font=self.label_font,
                width=3,
                bg="#ffffff"
            ).grid(row=i, column=0, padx=(0, 10), sticky="w")
            
            # 掌握度条
            bar_frame = tk.Frame(chart_frame, height=20, width=int(mastery[i]/100 * max_width), bg="#2196f3")
            bar_frame.grid(row=i, column=1, sticky="w")
            
            # 确保Frame保持固定大小
            bar_frame.pack_propagate(0)
            
            # 百分比标签
            tk.Label(
                chart_frame,
                text=f"{mastery[i]}%",
                font=self.label_font,
                bg="#ffffff"
            ).grid(row=i, column=2, padx=10, sticky="w")
        
        # 建议区域
        suggestion_frame = tk.Frame(stats_frame, bg="#f5f5f5", pady=10)
        suggestion_frame.pack(fill="x")
        
        tk.Label(
            suggestion_frame,
            text="建议加强练习：F4, B4",
            font=self.label_font,
            bg="#f5f5f5",
            fg="#f44336"
        ).pack(anchor="w")
    
    def export_data(self):
        """导出练习数据"""
        # 在实际应用中，这里会导出数据到CSV或Excel文件
        messagebox.showinfo("功能演示", "导出数据功能正在开发中")
    
    def clear_data(self):
        """清除练习数据"""
        # 确认对话框
        if messagebox.askyesno("确认", "确定要清除所有历史数据吗？此操作不可撤销。"):
            # 在实际应用中，这里会清除数据库或文件中的历史记录
            messagebox.showinfo("功能演示", "清除数据功能正在开发中")
            
            # 清空表格
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)


def main():
    root = tk.Tk()
    app = MainApp(root)
    root.mainloop()


if __name__ == "__main__":
    main() 